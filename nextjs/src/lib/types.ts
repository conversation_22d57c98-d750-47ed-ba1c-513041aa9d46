export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          phone: string | null
          first_name: string | null
          last_name: string | null
          avatar_url: string | null
          website: string | null
          bio: string | null
          address: string | null
          city: string | null
          state: string | null
          country: string | null
          postal_code: string | null
          role: 'user' | 'admin' | 'store_owner'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          phone?: string | null
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          website?: string | null
          bio?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          country?: string | null
          postal_code?: string | null
          role?: 'user' | 'admin' | 'store_owner'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          phone?: string | null
          first_name?: string | null
          last_name?: string | null
          avatar_url?: string | null
          website?: string | null
          bio?: string | null
          address?: string | null
          city?: string | null
          state?: string | null
          country?: string | null
          postal_code?: string | null
          role?: 'user' | 'admin' | 'store_owner'
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "profiles_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      },
      stores: {
        Row: {
          id: string
          name: string
          slug: string
          description: string | null
          logo: string | null
          cover_image: string | null
          owner_id: string
          contact_email: string | null
          contact_phone: string | null
          address: string
          status: 'active' | 'disabled' | 'suspended'
          featured: boolean
          rating: number
          review_count: number
          offers_delivery: boolean | null
          delivery_fee: number | null
          delivery_radius_km: number | null
          delivery_time_estimate: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description?: string | null
          logo?: string | null
          cover_image?: string | null
          owner_id: string
          contact_email?: string | null
          contact_phone?: string | null
          address: string
          status?: 'active' | 'disabled' | 'suspended'
          featured?: boolean
          rating?: number
          review_count?: number
          offers_delivery?: boolean | null
          delivery_fee?: number | null
          delivery_radius_km?: number | null
          delivery_time_estimate?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string | null
          logo?: string | null
          cover_image?: string | null
          owner_id?: string
          contact_email?: string | null
          contact_phone?: string | null
          address?: string
          status?: 'active' | 'disabled' | 'suspended'
          featured?: boolean
          rating?: number
          review_count?: number
          offers_delivery?: boolean | null
          delivery_fee?: number | null
          delivery_radius_km?: number | null
          delivery_time_estimate?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "stores_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      },
      cart: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          user_id: string
          product_id: string
          quantity: number
          options: Json | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id: string
          product_id: string
          quantity?: number
          options?: Json | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id?: string
          product_id?: string
          quantity?: number
          options?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "cart_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cart_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          }
        ]
      },
      products: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          name: string
          slug: string
          description: string | null
          price: number
          compare_at_price: number | null
          currency: string
          category_id: string | null
          store_id: string | null
          featured: boolean
          trending: boolean
          in_stock: boolean
          rating: number | null
          review_count: number | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          name: string
          slug: string
          description?: string | null
          price: number
          compare_at_price?: number | null
          currency?: string
          category_id?: string | null
          store_id?: string | null
          featured?: boolean
          trending?: boolean
          in_stock?: boolean
          rating?: number | null
          review_count?: number | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          slug?: string
          description?: string | null
          price?: number
          compare_at_price?: number | null
          currency?: string
          category_id?: string | null
          store_id?: string | null
          featured?: boolean
          trending?: boolean
          in_stock?: boolean
          rating?: number | null
          review_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "products_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_store_id_fkey"
            columns: ["store_id"]
            isOneToOne: false
            referencedRelation: "stores"
            referencedColumns: ["id"]
          }
        ]
      },
      external_delivery_services: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          name: string
          description: string | null
          logo: string | null
          contact_phone: string | null
          contact_email: string | null
          website: string | null
          base_fee: number
          per_km_fee: number | null
          minimum_order_amount: number | null
          maximum_delivery_radius_km: number | null
          estimated_delivery_time: string | null
          is_active: boolean
          coverage_areas: Json[] | null
          operating_hours: Json | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          name: string
          description?: string | null
          logo?: string | null
          contact_phone?: string | null
          contact_email?: string | null
          website?: string | null
          base_fee: number
          per_km_fee?: number | null
          minimum_order_amount?: number | null
          maximum_delivery_radius_km?: number | null
          estimated_delivery_time?: string | null
          is_active?: boolean
          coverage_areas?: Json[] | null
          operating_hours?: Json | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          description?: string | null
          logo?: string | null
          contact_phone?: string | null
          contact_email?: string | null
          website?: string | null
          base_fee?: number
          per_km_fee?: number | null
          minimum_order_amount?: number | null
          maximum_delivery_radius_km?: number | null
          estimated_delivery_time?: string | null
          is_active?: boolean
          coverage_areas?: Json[] | null
          operating_hours?: Json | null
        }
        Relationships: []
      },
      orders: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          user_id: string
          subtotal: number | null
          service_fee: number | null
          delivery_fee: number | null
          total: number | null
          currency: string | null
          delivery_method: 'delivery' | 'pickup' | 'courier' | null
          status: string | null
          shipping_name: string | null
          shipping_email: string | null
          shipping_phone: string | null
          shipping_address: string | null
          shipping_city: string | null
          shipping_state: string | null
          shipping_country: string | null
          shipping_postal_code: string | null
          notes: string | null
          selected_delivery_service_id: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id: string
          subtotal?: number | null
          service_fee?: number | null
          delivery_fee?: number | null
          total?: number | null
          currency?: string | null
          delivery_method?: 'delivery' | 'pickup' | 'courier' | null
          status?: string | null
          shipping_name?: string | null
          shipping_email?: string | null
          shipping_phone?: string | null
          shipping_address?: string | null
          shipping_city?: string | null
          shipping_state?: string | null
          shipping_country?: string | null
          shipping_postal_code?: string | null
          notes?: string | null
          selected_delivery_service_id?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id?: string
          subtotal?: number | null
          service_fee?: number | null
          delivery_fee?: number | null
          total?: number | null
          currency?: string | null
          delivery_method?: 'delivery' | 'pickup' | 'courier' | null
          status?: string | null
          shipping_name?: string | null
          shipping_email?: string | null
          shipping_phone?: string | null
          shipping_address?: string | null
          shipping_city?: string | null
          shipping_state?: string | null
          shipping_country?: string | null
          shipping_postal_code?: string | null
          notes?: string | null
          selected_delivery_service_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "orders_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_selected_delivery_service_id_fkey"
            columns: ["selected_delivery_service_id"]
            isOneToOne: false
            referencedRelation: "external_delivery_services"
            referencedColumns: ["id"]
          }
        ]
      },
      todo_list: {
        Row: {
          created_at: string
          description: string | null
          done: boolean
          done_at: string | null
          id: number
          owner: string
          title: string
          urgent: boolean
        }
        Insert: {
          created_at?: string
          description?: string | null
          done?: boolean
          done_at?: string | null
          id?: number
          owner: string
          title: string
          urgent?: boolean
        }
        Update: {
          created_at?: string
          description?: string | null
          done?: boolean
          done_at?: string | null
          id?: number
          owner?: string
          title?: string
          urgent?: boolean
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
