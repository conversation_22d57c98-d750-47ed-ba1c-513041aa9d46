import { createSPASassClient } from '@/lib/supabase/client';
import { CartItem, Order } from '@/lib/types/ecommerce';

interface CreateOrderParams {
  userId: string;
  cartItems: CartItem[];
  subtotal: number;
  serviceFee: number;
  deliveryFee: number;
  total: number;
  currency: string;
  deliveryMethod: 'delivery' | 'pickup';
  shippingAddress?: {
    name?: string;
    email?: string;
    phone?: string;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
  notes?: string;
  selected_delivery_service_id?: string | null; // Added this line
}

interface CreatePaymentParams {
  orderId: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  transactionId: string;
  paymentDetails?: Record<string, any>;
}

export class CheckoutService {
  /**
   * Calculate service fee based on subtotal using dynamic configuration
   */
  static async calculateServiceFee(subtotal: number): Promise<number> {
    try {
      const { SystemSettingsService } = await import('./system-settings');
      return await SystemSettingsService.calculateServiceFee(subtotal);
    } catch (error) {
      console.error('Error calculating service fee, using fallback:', error);
      // Fallback to Dalasi amounts
      return subtotal > 1000 ? 25.00 : 10.00;
    }
  }

  /**
   * Calculate service fee synchronously (legacy method for backward compatibility)
   * @deprecated Use calculateServiceFee instead
   */
  static calculateServiceFeeSync(subtotal: number): number {
    // Fallback to Dalasi amounts for sync calls
    return subtotal > 1000 ? 25.00 : 10.00;
  }

  /**
   * Calculate delivery fee based on delivery method
   */
  static calculateDeliveryFee(deliveryMethod: 'delivery' | 'pickup'): number {
    console.log('[CheckoutService] calculateDeliveryFee called with method:', deliveryMethod);
    let fee = 0;
    if (deliveryMethod === 'delivery') {
      fee = 15.00; // Fixed delivery fee in GMD
    }
    console.log('[CheckoutService] calculateDeliveryFee returning fee:', fee);
    return fee; // No fee for pickup if not delivery
  }

  /**
   * Check inventory availability for cart items
   */
  static async checkInventoryAvailability(cartItems: CartItem[]): Promise<{ available: boolean; outOfStockItems: string[] }> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const productIds = cartItems.map(item => item.product_id);

      // Define a local type for the expected product structure
      interface ProductWithInventory {
        id: string;
        name: string;
        inventory_quantity: number;
      }

      const { data: productsData, error } = await supabase
        .from('products')
        .select('id, name, inventory_quantity')
        .in('id', productIds);

      if (error) throw error;

      const products = productsData as unknown as ProductWithInventory[] | null; // Assert type here, casting through unknown

      const outOfStockItems: string[] = [];

      for (const item of cartItems) {
        const product = products?.find(p => p.id === item.product_id);
        // Assuming inventory_quantity is now correctly typed on 'product'
        if (!product || product.inventory_quantity < item.quantity) {
          outOfStockItems.push(product?.name || `Product ${item.product_id}`);
        }
      }

      return {
        available: outOfStockItems.length === 0,
        outOfStockItems
      };
    } catch (error) {
      console.error('Error checking inventory:', error);
      return { available: false, outOfStockItems: [] };
    }
  }

  /**
   * Fetch complete product data for cart items
   * Use this to ensure all cart items have complete product data before checkout
   */
  static async enrichCartItems(cartItems: CartItem[]): Promise<CartItem[]> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      // Get product IDs that need to be fetched
      const productIds = cartItems.map(item => item.product_id);

      // Fetch complete product data including store_id
      const { data: products, error } = await supabase
        .from('products')
        .select('*, store_id')
        .in('id', productIds);

      if (error) throw error;

      // Create a map of products by ID for quick lookup
      const productMap = new Map();
      products.forEach(product => {
        productMap.set(product.id, product);
      });

      // Enrich cart items with complete product data
      return cartItems.map(item => ({
        ...item,
        product: productMap.get(item.product_id) || item.product
      }));
    } catch (error) {
      console.error('Error enriching cart items:', error);
      return cartItems; // Return original items if enrichment fails
    }
  }
  /**
   * Create a new order from cart items
   */
  static async createOrder(params: CreateOrderParams): Promise<{ id: string; status: string }> {
    try {
      // Always enrich cart items first to ensure we have complete product data
      const enrichedCartItems = await this.enrichCartItems(params.cartItems);

      // Check for missing store_ids after enrichment
      const missingStoreProducts = enrichedCartItems.filter(
        item => !item.product || !(item.product as any)['store_id'] // Use 'as any' and bracket notation
      );

      if (missingStoreProducts.length > 0) {
        const missingIds = missingStoreProducts.map(item => item.product_id).join(', ');
        throw new Error(`Store ID is missing for the following products: ${missingIds}. Please remove these items from your cart and try again.`);
      }

      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      // Create the order with enriched cart items
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          user_id: params.userId,
          subtotal: params.subtotal,
          service_fee: params.serviceFee,
          delivery_fee: params.deliveryFee,
          total: params.total,
          currency: params.currency,
          delivery_method: params.deliveryMethod,
          status: 'pending',
          shipping_name: params.shippingAddress?.name,
          shipping_email: params.shippingAddress?.email,
          shipping_phone: params.shippingAddress?.phone,
          shipping_address: params.shippingAddress?.address,
          shipping_city: params.shippingAddress?.city,
          shipping_state: params.shippingAddress?.state,
          shipping_country: params.shippingAddress?.country,
          shipping_postal_code: params.shippingAddress?.postalCode,
          notes: params.notes,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (orderError) throw orderError;

      // Helper function to get effective price (considering flash sale prices)
      const getEffectivePrice = (product: any) => {
        // Check if product has a flash sale (compare_at_price exists and is greater than current price)
        if (product?.compare_at_price && product.compare_at_price > product.price) {
          return product.price; // Already discounted price
        }
        return product?.price || 0;
      };

      // Create order items using the enriched cart items with effective pricing
      const orderItems = enrichedCartItems.map(item => {
        const effectivePrice = getEffectivePrice(item.product);
        return {
          order_id: order.id,
          product_id: item.product_id,
          store_id: (item.product as any)['store_id'], // Use 'as any' and bracket notation
          quantity: item.quantity,
          price: effectivePrice,
          total: effectivePrice * item.quantity,
          currency: params.currency,
          created_at: new Date().toISOString()
        };
      });

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems);

      if (itemsError) throw itemsError;

      // Create order_store_items manually (instead of relying on trigger)
      // Group order items by store and calculate totals using effective pricing
      const storeGroups = enrichedCartItems.reduce((groups, item) => {
        const storeId = (item.product as any)['store_id']; // Use 'as any' and bracket notation
        if (!storeId) {
          console.warn(`Product ${item.product_id} is missing a store_id in enrichedCartItems.`);
          return groups;
        }
        if (!groups[storeId]) {
          groups[storeId] = {
            store_id: storeId,
            total_amount: 0,
            currency: params.currency
          };
        }
        const effectivePrice = getEffectivePrice(item.product);
        groups[storeId].total_amount += effectivePrice * item.quantity;
        return groups;
      }, {} as Record<string, { store_id: string; total_amount: number; currency: string }>);

      // Insert order_store_items for each store
      const orderStoreItems = Object.values(storeGroups).map(group => ({
        order_id: order.id,
        store_id: group.store_id,
        total_amount: group.total_amount,
        currency: group.currency,
        status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      if (orderStoreItems.length > 0) {
        const { error: storeItemsError } = await supabase
          .from('order_store_items')
          .upsert(orderStoreItems, {
            onConflict: 'order_id,store_id',
            ignoreDuplicates: true
          });

        if (storeItemsError) {
          console.error('Error creating order_store_items:', storeItemsError);
          // Don't throw error here - the order was created successfully
          // This is just for store management purposes
        }
      }

      // Clear the cart
      console.log(`[CheckoutService] Attempting to clear cart for userId: ${params.userId}`);
      // Add .select() to ensure we get back data about what was deleted, which helps confirm the operation.
      // If RLS prevents deletion, 'data' would be empty and 'error' might indicate RLS issue or just return no error.
      const { error: clearCartError, data: deletedCartItems, count: deleteCount } = await supabase
        .from('cart')
        .delete()
        .eq('user_id', params.userId)
        .select(); // Request the deleted rows

      if (clearCartError) {
        console.error('[CheckoutService] Error object when clearing cart:', JSON.stringify(clearCartError, null, 2));
        // Continue even if cart clearing fails
      } else {
        console.log(`[CheckoutService] Cart clear operation for userId: ${params.userId}.`);
        console.log('[CheckoutService] Deleted cart items:', JSON.stringify(deletedCartItems, null, 2));
        console.log('[CheckoutService] Count of deleted items (from client if available, may be null):', deleteCount);
        if (deletedCartItems && deletedCartItems.length > 0) {
          console.log(`[CheckoutService] Successfully deleted ${deletedCartItems.length} item(s) from cart.`);
        } else if (deletedCartItems) { // deletedCartItems is not null, but could be empty array
          console.log('[CheckoutService] No items were deleted from cart (deletedCartItems array is empty). This might mean the cart was already empty or RLS prevented deletion.');
        } else {
          console.log('[CheckoutService] No data returned from delete operation (deletedCartItems is null/undefined). Delete count was:', deleteCount);
        }
      }

      // Verify cart state immediately after deletion attempt
      try {
        const { data: cartAfterDelete, error: verifyError } = await supabase
          .from('cart')
          .select('*')
          .eq('user_id', params.userId);

        if (verifyError) {
          console.error('[CheckoutService] Error verifying cart after delete:', JSON.stringify(verifyError, null, 2));
        } else {
          console.log('[CheckoutService] Cart contents immediately after delete attempt for userId', params.userId, ':', JSON.stringify(cartAfterDelete, null, 2));
          if (cartAfterDelete && cartAfterDelete.some(item => item.id === (deletedCartItems && deletedCartItems[0]?.id))) {
            console.error(`[CheckoutService] CRITICAL: Item ${deletedCartItems && deletedCartItems[0]?.id} reported as deleted but still found in cart immediately after!`);
          } else if (cartAfterDelete && cartAfterDelete.length === 0) {
            console.log(`[CheckoutService] Cart is confirmed empty for user ${params.userId} immediately after delete.`);
          }
        }
      } catch (e) {
        console.error('[CheckoutService] Exception during post-delete cart verification:', e);
      }


      return { id: order.id, status: 'pending' };
    } catch (error) {
      console.error('Error creating order:', error);
      if (error instanceof Error) {
        throw new Error(error.message || 'Failed to create order');
      }
      throw new Error('Failed to create order due to an unknown error');
    }
  }

  /**
   * Create a payment record for an order
   */
  static async createPayment(params: CreatePaymentParams): Promise<{ id: string; status: string }> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data: payment, error } = await supabase
        .from('payments')
        .insert({
          order_id: params.orderId,
          amount: params.amount,
          currency: params.currency,
          payment_method: params.paymentMethod,
          payment_status: 'pending',
          transaction_id: params.transactionId,
          payment_details: params.paymentDetails || {},
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (error) throw error;

      return { id: payment.id as string, status: 'pending' }; // Cast payment.id to string
    } catch (error) {
      console.error('Error creating payment:', error);
      throw new Error('Failed to create payment');
    }
  }

  /**
   * Get order details by ID
   */
  static async getOrder(orderId: string, userId: string): Promise<Order | null> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          items:order_items(
            *,
            product:products(*)
          )
        `)
        .eq('id', orderId)
        .eq('user_id', userId)
        .single();

      if (error) throw error;

      return data as unknown as Order;
    } catch (error) {
      console.error('Error getting order:', error);
      return null;
    }
  }

  /**
   * Get payment status for an order
   */
  static async getPaymentStatus(orderId: string): Promise<string> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data, error } = await supabase
        .from('payments')
        .select('payment_status')
        .eq('order_id', orderId)
        .single();

      if (error) throw error;

      return (data as any).payment_status; // Cast data to any to access payment_status
    } catch (error) {
      console.error('Error getting payment status:', error);
      return 'unknown';
    }
  }
}