import {SupabaseClient} from "@supabase/supabase-js";
import {Database} from "./database.types";

export enum ClientType {
    SERVER = 'server',
    SPA = 'spa'
}

export class SassClient {
    private client: SupabaseClient<Database>;
    private clientType: ClientType;

    constructor(client: SupabaseClient, clientType: ClientType) {
        this.client = client;
        this.clientType = clientType;

    }

    async loginEmail(email: string, password: string) {
        return this.client.auth.signInWithPassword({
            email: email,
            password: password
        });
    }

    async registerEmail(email: string, password: string) {
        return this.client.auth.signUp({
            email: email,
            password: password
        });
    }

    async exchangeCodeForSession(code: string) {
        return this.client.auth.exchangeCodeForSession(code);
    }

    async resendVerificationEmail(email: string) {
        return this.client.auth.resend({
            email: email,
            type: 'signup'
        })
    }

    async logout() {
        const { error } = await this.client.auth.signOut({
            scope: 'local'
        });
        if (error) throw error;
        if(this.clientType === ClientType.SPA) {
            window.location.href = '/auth/login';
        }
    }

    async uploadFile(myId: string, filename: string, file: File) {
        filename = filename.replace(/[^0-9a-zA-Z!\-_.*'()]/g, '_');
        filename = myId + "/" + filename
        return this.client.storage.from('files').upload(filename, file);
    }

    async getFiles(myId: string) {
        return this.client.storage.from('files').list(myId)
    }

    async deleteFile(myId: string, filename: string) {
        filename = myId + "/" + filename
        return this.client.storage.from('files').remove([filename])
    }

    async shareFile(myId: string, filename: string, timeInSec: number, forDownload: boolean = false) {
        filename = myId + "/" + filename
        return this.client.storage.from('files').createSignedUrl(filename, timeInSec, {
            download: forDownload
        });

    }

    async getMyTodoList(page: number = 1, pageSize: number = 100, order: string = 'created_at', done: boolean | null = false) {
        let query = this.client.from('todo_list').select('*').range(page * pageSize - pageSize, page * pageSize - 1).order(order)
        if (done !== null) {
            query = query.eq('done', done)
        }
        return query
    }

    async createTask(row: Database["public"]["Tables"]["todo_list"]["Insert"]) {
        return this.client.from('todo_list').insert(row)
    }

    async removeTask (id: string) {
        return this.client.from('todo_list').delete().eq('id', id)
    }

    async updateAsDone (id: string) {
        return this.client.from('todo_list').update({done: true}).eq('id', id)
    }

    async getUserProfile() {
        const { data: { user } } = await this.client.auth.getUser();
        if (!user) {
            return { data: null, error: { message: 'User not authenticated' } };
        }

        return this.client
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();
    }

    async updateUserProfile(params: Database["public"]["Tables"]["profiles"]["Update"]) {
        const { data: { user } } = await this.client.auth.getUser();
        if (!user) {
            return { data: null, error: { message: 'User not authenticated' } };
        }

        return this.client
            .from('profiles')
            .update({
                ...params,
                updated_at: new Date().toISOString()
            })
            .eq('id', user.id)
            .select()
            .single();
    }

    async changePassword(_currentPassword: string, newPassword: string) {
        return this.client.auth.updateUser({
            password: newPassword
        });
    }

    getSupabaseClient() {
        return this.client;
    }


}
