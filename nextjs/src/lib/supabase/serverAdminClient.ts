import { createClient } from '@supabase/supabase-js'
import { Database } from "./database.types";
import { createSPASassClient } from './client';

/**
 * Creates a Supabase client with admin privileges for server-side operations
 * This bypasses RLS policies and should only be used in server-side API routes
 */
export async function createServerAdminClient() {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.PRIVATE_SUPABASE_SERVICE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
        console.warn('Missing Supabase environment variables for admin client, falling back to regular client');
        // Fallback to regular client for development purposes
        const client = await createSPASassClient();
        return client.getSupabaseClient();
    }

    return createClient<Database>(
        supabaseUrl,
        supabaseServiceKey,
        {
            auth: {
                autoRefreshToken: false,
                persistSession: false
            }
        }
    );
}