export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      cart: {
        Row: {
          created_at: string | null
          id: string
          options: Json | null
          product_id: string
          quantity: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          options?: Json | null
          product_id: string
          quantity?: number
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          options?: Json | null
          product_id?: string
          quantity?: number
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "cart_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      categories: {
        Row: {
          created_at: string | null
          description: string | null
          featured: boolean
          id: string
          image: string | null
          name: string
          parent_id: string | null
          slug: string
          thumbnail: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          featured?: boolean
          id?: string
          image?: string | null
          name: string
          parent_id?: string | null
          slug: string
          thumbnail?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          featured?: boolean
          id?: string
          image?: string | null
          name?: string
          parent_id?: string | null
          slug?: string
          thumbnail?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "categories_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      confirmed_payments: {
        Row: {
          confirmed_at: string | null
          confirmed_by: string
          created_at: string | null
          id: string
          inventory_updated: boolean | null
          order_id: string
          payment_id: string
          store_notified: boolean | null
        }
        Insert: {
          confirmed_at?: string | null
          confirmed_by: string
          created_at?: string | null
          id?: string
          inventory_updated?: boolean | null
          order_id: string
          payment_id: string
          store_notified?: boolean | null
        }
        Update: {
          confirmed_at?: string | null
          confirmed_by?: string
          created_at?: string | null
          id?: string
          inventory_updated?: boolean | null
          order_id?: string
          payment_id?: string
          store_notified?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "confirmed_payments_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "confirmed_payments_payment_id_fkey"
            columns: ["payment_id"]
            isOneToOne: false
            referencedRelation: "payments"
            referencedColumns: ["id"]
          },
        ]
      }
      courier_services: {
        Row: {
          base_cost: number
          contact_email: string | null
          contact_phone: string | null
          cost_per_kg: number | null
          countries: string[] | null
          created_at: string | null
          description: string | null
          estimated_delivery_time: string | null
          free_weight_limit: number | null
          id: string
          is_active: boolean | null
          logo: string | null
          name: string
          tracking_supported: boolean | null
          updated_at: string | null
          website: string | null
        }
        Insert: {
          base_cost?: number
          contact_email?: string | null
          contact_phone?: string | null
          cost_per_kg?: number | null
          countries?: string[] | null
          created_at?: string | null
          description?: string | null
          estimated_delivery_time?: string | null
          free_weight_limit?: number | null
          id?: string
          is_active?: boolean | null
          logo?: string | null
          name: string
          tracking_supported?: boolean | null
          updated_at?: string | null
          website?: string | null
        }
        Update: {
          base_cost?: number
          contact_email?: string | null
          contact_phone?: string | null
          cost_per_kg?: number | null
          countries?: string[] | null
          created_at?: string | null
          description?: string | null
          estimated_delivery_time?: string | null
          free_weight_limit?: number | null
          id?: string
          is_active?: boolean | null
          logo?: string | null
          name?: string
          tracking_supported?: boolean | null
          updated_at?: string | null
          website?: string | null
        }
        Relationships: []
      }
      deals: {
        Row: {
          created_at: string | null
          currency: string
          deal_price: number
          deal_type: string
          description: string | null
          discount_percentage: number | null
          end_date: string
          featured: boolean
          id: string
          is_active: boolean
          max_quantity: number | null
          original_price: number
          product_id: string
          start_date: string
          title: string
          updated_at: string | null
          used_quantity: number | null
        }
        Insert: {
          created_at?: string | null
          currency?: string
          deal_price: number
          deal_type?: string
          description?: string | null
          discount_percentage?: number | null
          end_date: string
          featured?: boolean
          id?: string
          is_active?: boolean
          max_quantity?: number | null
          original_price: number
          product_id: string
          start_date?: string
          title: string
          updated_at?: string | null
          used_quantity?: number | null
        }
        Update: {
          created_at?: string | null
          currency?: string
          deal_price?: number
          deal_type?: string
          description?: string | null
          discount_percentage?: number | null
          end_date?: string
          featured?: boolean
          id?: string
          is_active?: boolean
          max_quantity?: number | null
          original_price?: number
          product_id?: string
          start_date?: string
          title?: string
          updated_at?: string | null
          used_quantity?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "deals_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      delivery_service_pricing: {
        Row: {
          area_name: string
          base_fee: number
          created_at: string | null
          id: string
          is_active: boolean | null
          maximum_distance_km: number | null
          minimum_order_amount: number | null
          per_km_fee: number | null
          service_id: string
          updated_at: string | null
        }
        Insert: {
          area_name: string
          base_fee: number
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          maximum_distance_km?: number | null
          minimum_order_amount?: number | null
          per_km_fee?: number | null
          service_id: string
          updated_at?: string | null
        }
        Update: {
          area_name?: string
          base_fee?: number
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          maximum_distance_km?: number | null
          minimum_order_amount?: number | null
          per_km_fee?: number | null
          service_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "delivery_service_pricing_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "external_delivery_services"
            referencedColumns: ["id"]
          },
        ]
      }
      external_delivery_services: {
        Row: {
          base_fee: number
          contact_email: string | null
          contact_phone: string | null
          coverage_areas: string[] | null
          created_at: string | null
          description: string | null
          estimated_delivery_time: string | null
          id: string
          is_active: boolean | null
          logo: string | null
          maximum_delivery_radius_km: number | null
          minimum_order_amount: number | null
          name: string
          operating_hours: Json | null
          per_km_fee: number | null
          updated_at: string | null
          website: string | null
        }
        Insert: {
          base_fee?: number
          contact_email?: string | null
          contact_phone?: string | null
          coverage_areas?: string[] | null
          created_at?: string | null
          description?: string | null
          estimated_delivery_time?: string | null
          id?: string
          is_active?: boolean | null
          logo?: string | null
          maximum_delivery_radius_km?: number | null
          minimum_order_amount?: number | null
          name: string
          operating_hours?: Json | null
          per_km_fee?: number | null
          updated_at?: string | null
          website?: string | null
        }
        Update: {
          base_fee?: number
          contact_email?: string | null
          contact_phone?: string | null
          coverage_areas?: string[] | null
          created_at?: string | null
          description?: string | null
          estimated_delivery_time?: string | null
          id?: string
          is_active?: boolean | null
          logo?: string | null
          maximum_delivery_radius_km?: number | null
          minimum_order_amount?: number | null
          name?: string
          operating_hours?: Json | null
          per_km_fee?: number | null
          updated_at?: string | null
          website?: string | null
        }
        Relationships: []
      }
      feature_toggles: {
        Row: {
          created_at: string | null
          description: string | null
          enabled: boolean | null
          feature_key: string
          feature_name: string
          id: string
          role_restrictions: string[] | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          enabled?: boolean | null
          feature_key: string
          feature_name: string
          id?: string
          role_restrictions?: string[] | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          enabled?: boolean | null
          feature_key?: string
          feature_name?: string
          id?: string
          role_restrictions?: string[] | null
          updated_at?: string | null
        }
        Relationships: []
      }
      finder_earnings: {
        Row: {
          amount: number
          created_at: string | null
          currency: string
          earning_date: string
          id: string
          order_id: string
          payout_id: string | null
          updated_at: string | null
        }
        Insert: {
          amount: number
          created_at?: string | null
          currency?: string
          earning_date?: string
          id?: string
          order_id: string
          payout_id?: string | null
          updated_at?: string | null
        }
        Update: {
          amount?: number
          created_at?: string | null
          currency?: string
          earning_date?: string
          id?: string
          order_id?: string
          payout_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "finder_earnings_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "finder_earnings_payout_id_fkey"
            columns: ["payout_id"]
            isOneToOne: false
            referencedRelation: "payouts"
            referencedColumns: ["id"]
          },
        ]
      }
      hero_slides: {
        Row: {
          background_color: string | null
          created_at: string | null
          id: string
          image_url: string
          is_active: boolean
          link_text: string | null
          link_url: string | null
          position: number
          subtitle: string | null
          text_color: string | null
          title: string
          updated_at: string | null
        }
        Insert: {
          background_color?: string | null
          created_at?: string | null
          id?: string
          image_url: string
          is_active?: boolean
          link_text?: string | null
          link_url?: string | null
          position?: number
          subtitle?: string | null
          text_color?: string | null
          title: string
          updated_at?: string | null
        }
        Update: {
          background_color?: string | null
          created_at?: string | null
          id?: string
          image_url?: string
          is_active?: boolean
          link_text?: string | null
          link_url?: string | null
          position?: number
          subtitle?: string | null
          text_color?: string | null
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      inventory_transactions: {
        Row: {
          created_at: string | null
          created_by: string | null
          id: string
          new_quantity: number
          order_id: string | null
          previous_quantity: number
          product_id: string
          quantity_change: number
          reason: string | null
          transaction_type: string
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          new_quantity: number
          order_id?: string | null
          previous_quantity: number
          product_id: string
          quantity_change: number
          reason?: string | null
          transaction_type: string
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          new_quantity?: number
          order_id?: string | null
          previous_quantity?: number
          product_id?: string
          quantity_change?: number
          reason?: string | null
          transaction_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "inventory_transactions_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "inventory_transactions_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          content: string
          created_at: string | null
          data: Json | null
          id: string
          message: string
          notification_category: string
          read: boolean
          title: string
          type: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          data?: Json | null
          id?: string
          message: string
          notification_category?: string
          read?: boolean
          title: string
          type: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string | null
          data?: Json | null
          id?: string
          message?: string
          notification_category?: string
          read?: boolean
          title?: string
          type?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      order_items: {
        Row: {
          created_at: string | null
          currency: string
          id: string
          order_id: string
          price: number
          product_id: string
          quantity: number
          store_id: string
          total: number
          vendor_id: string | null
        }
        Insert: {
          created_at?: string | null
          currency?: string
          id?: string
          order_id: string
          price: number
          product_id: string
          quantity: number
          store_id: string
          total: number
          vendor_id?: string | null
        }
        Update: {
          created_at?: string | null
          currency?: string
          id?: string
          order_id?: string
          price?: number
          product_id?: string
          quantity?: number
          store_id?: string
          total?: number
          vendor_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "order_items_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_items_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_items_store_id_fkey"
            columns: ["store_id"]
            isOneToOne: false
            referencedRelation: "stores"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_items_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "stores_compatibility"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_items_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      order_store_items: {
        Row: {
          accepted_at: string | null
          created_at: string | null
          currency: string
          id: string
          order_id: string
          rejected_at: string | null
          rejection_notes: string | null
          rejection_reason: string | null
          status: string
          store_id: string
          total_amount: number
          updated_at: string | null
        }
        Insert: {
          accepted_at?: string | null
          created_at?: string | null
          currency?: string
          id?: string
          order_id: string
          rejected_at?: string | null
          rejection_notes?: string | null
          rejection_reason?: string | null
          status?: string
          store_id: string
          total_amount: number
          updated_at?: string | null
        }
        Update: {
          accepted_at?: string | null
          created_at?: string | null
          currency?: string
          id?: string
          order_id?: string
          rejected_at?: string | null
          rejection_notes?: string | null
          rejection_reason?: string | null
          status?: string
          store_id?: string
          total_amount?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "order_store_items_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_store_items_store_id_fkey"
            columns: ["store_id"]
            isOneToOne: false
            referencedRelation: "stores"
            referencedColumns: ["id"]
          },
        ]
      }
      order_vendor_items: {
        Row: {
          accepted_at: string | null
          created_at: string | null
          currency: string
          id: string
          order_id: string
          rejected_at: string | null
          rejection_notes: string | null
          rejection_reason: string | null
          status: string
          total_amount: number
          updated_at: string | null
          vendor_id: string
        }
        Insert: {
          accepted_at?: string | null
          created_at?: string | null
          currency?: string
          id?: string
          order_id: string
          rejected_at?: string | null
          rejection_notes?: string | null
          rejection_reason?: string | null
          status?: string
          total_amount: number
          updated_at?: string | null
          vendor_id: string
        }
        Update: {
          accepted_at?: string | null
          created_at?: string | null
          currency?: string
          id?: string
          order_id?: string
          rejected_at?: string | null
          rejection_notes?: string | null
          rejection_reason?: string | null
          status?: string
          total_amount?: number
          updated_at?: string | null
          vendor_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "order_vendor_items_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_vendor_items_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "stores_compatibility"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "order_vendor_items_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      orders: {
        Row: {
          created_at: string | null
          currency: string
          delivery_fee: number | null
          delivery_method: string | null
          id: string
          notes: string | null
          selected_delivery_service_id: string | null
          service_fee: number | null
          shipping_address: string | null
          shipping_city: string | null
          shipping_country: string | null
          shipping_email: string | null
          shipping_name: string | null
          shipping_phone: string | null
          shipping_postal_code: string | null
          shipping_state: string | null
          status: string
          subtotal: number | null
          total: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          currency?: string
          delivery_fee?: number | null
          delivery_method?: string | null
          id?: string
          notes?: string | null
          selected_delivery_service_id?: string | null
          service_fee?: number | null
          shipping_address?: string | null
          shipping_city?: string | null
          shipping_country?: string | null
          shipping_email?: string | null
          shipping_name?: string | null
          shipping_phone?: string | null
          shipping_postal_code?: string | null
          shipping_state?: string | null
          status?: string
          subtotal?: number | null
          total: number
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          currency?: string
          delivery_fee?: number | null
          delivery_method?: string | null
          id?: string
          notes?: string | null
          selected_delivery_service_id?: string | null
          service_fee?: number | null
          shipping_address?: string | null
          shipping_city?: string | null
          shipping_country?: string | null
          shipping_email?: string | null
          shipping_name?: string | null
          shipping_phone?: string | null
          shipping_postal_code?: string | null
          shipping_state?: string | null
          status?: string
          subtotal?: number | null
          total?: number
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "orders_selected_delivery_service_id_fkey"
            columns: ["selected_delivery_service_id"]
            isOneToOne: false
            referencedRelation: "external_delivery_services"
            referencedColumns: ["id"]
          },
        ]
      }
      payments: {
        Row: {
          amount: number
          created_at: string | null
          currency: string
          id: string
          order_id: string
          payment_details: Json | null
          payment_method: string
          payment_status: string
          transaction_id: string | null
          updated_at: string | null
        }
        Insert: {
          amount: number
          created_at?: string | null
          currency?: string
          id?: string
          order_id: string
          payment_details?: Json | null
          payment_method: string
          payment_status?: string
          transaction_id?: string | null
          updated_at?: string | null
        }
        Update: {
          amount?: number
          created_at?: string | null
          currency?: string
          id?: string
          order_id?: string
          payment_details?: Json | null
          payment_method?: string
          payment_status?: string
          transaction_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payments_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
        ]
      }
      payout_schedule: {
        Row: {
          created_at: string | null
          id: string
          net_amount: number
          payout_date: string
          processed_at: string | null
          service_fees_deducted: number
          status: string
          store_id: string
          total_amount: number
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          net_amount?: number
          payout_date: string
          processed_at?: string | null
          service_fees_deducted?: number
          status?: string
          store_id: string
          total_amount?: number
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          net_amount?: number
          payout_date?: string
          processed_at?: string | null
          service_fees_deducted?: number
          status?: string
          store_id?: string
          total_amount?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payout_schedule_store_id_fkey"
            columns: ["store_id"]
            isOneToOne: false
            referencedRelation: "stores"
            referencedColumns: ["id"]
          },
        ]
      }
      payouts: {
        Row: {
          amount: number
          commission: number
          created_at: string | null
          currency: string
          id: string
          notes: string | null
          order_id: string
          payout_details: Json | null
          payout_status: string
          store_id: string
          transaction_id: string | null
          updated_at: string | null
          vendor_id: string | null
        }
        Insert: {
          amount: number
          commission: number
          created_at?: string | null
          currency?: string
          id?: string
          notes?: string | null
          order_id: string
          payout_details?: Json | null
          payout_status?: string
          store_id: string
          transaction_id?: string | null
          updated_at?: string | null
          vendor_id?: string | null
        }
        Update: {
          amount?: number
          commission?: number
          created_at?: string | null
          currency?: string
          id?: string
          notes?: string | null
          order_id?: string
          payout_details?: Json | null
          payout_status?: string
          store_id?: string
          transaction_id?: string | null
          updated_at?: string | null
          vendor_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payouts_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payouts_store_id_fkey"
            columns: ["store_id"]
            isOneToOne: false
            referencedRelation: "stores"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payouts_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "stores_compatibility"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payouts_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      product_images: {
        Row: {
          alt: string | null
          created_at: string | null
          id: string
          position: number
          product_id: string
          url: string
        }
        Insert: {
          alt?: string | null
          created_at?: string | null
          id?: string
          position?: number
          product_id: string
          url: string
        }
        Update: {
          alt?: string | null
          created_at?: string | null
          id?: string
          position?: number
          product_id?: string
          url?: string
        }
        Relationships: [
          {
            foreignKeyName: "product_images_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      product_shipping: {
        Row: {
          courier_service_id: string | null
          created_at: string | null
          delivery_service_id: string | null
          id: string
          product_id: string
          shipping_type: string
          updated_at: string | null
        }
        Insert: {
          courier_service_id?: string | null
          created_at?: string | null
          delivery_service_id?: string | null
          id?: string
          product_id: string
          shipping_type: string
          updated_at?: string | null
        }
        Update: {
          courier_service_id?: string | null
          created_at?: string | null
          delivery_service_id?: string | null
          id?: string
          product_id?: string
          shipping_type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "product_shipping_courier_service_id_fkey"
            columns: ["courier_service_id"]
            isOneToOne: false
            referencedRelation: "courier_services"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_shipping_delivery_service_id_fkey"
            columns: ["delivery_service_id"]
            isOneToOne: false
            referencedRelation: "external_delivery_services"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "product_shipping_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: true
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      product_specifications: {
        Row: {
          created_at: string | null
          id: string
          name: string
          product_id: string
          value: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
          product_id: string
          value: string
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          product_id?: string
          value?: string
        }
        Relationships: [
          {
            foreignKeyName: "product_specifications_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      products: {
        Row: {
          category_id: string | null
          compare_at_price: number | null
          created_at: string | null
          currency: string
          description: string | null
          featured: boolean
          id: string
          in_stock: boolean
          inventory_quantity: number | null
          is_local: boolean | null
          name: string
          price: number
          rating: number | null
          review_count: number | null
          slug: string
          store_id: string | null
          trending: boolean
          updated_at: string | null
          vendor_id: string | null
          weight: number | null
        }
        Insert: {
          category_id?: string | null
          compare_at_price?: number | null
          created_at?: string | null
          currency?: string
          description?: string | null
          featured?: boolean
          id?: string
          in_stock?: boolean
          inventory_quantity?: number | null
          is_local?: boolean | null
          name: string
          price: number
          rating?: number | null
          review_count?: number | null
          slug: string
          store_id?: string | null
          trending?: boolean
          updated_at?: string | null
          vendor_id?: string | null
          weight?: number | null
        }
        Update: {
          category_id?: string | null
          compare_at_price?: number | null
          created_at?: string | null
          currency?: string
          description?: string | null
          featured?: boolean
          id?: string
          in_stock?: boolean
          inventory_quantity?: number | null
          is_local?: boolean | null
          name?: string
          price?: number
          rating?: number | null
          review_count?: number | null
          slug?: string
          store_id?: string | null
          trending?: boolean
          updated_at?: string | null
          vendor_id?: string | null
          weight?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "products_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_store_id_fkey"
            columns: ["store_id"]
            isOneToOne: false
            referencedRelation: "stores"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "stores_compatibility"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          address: string | null
          avatar_url: string | null
          bio: string | null
          city: string | null
          country: string | null
          created_at: string | null
          email: string
          first_name: string | null
          id: string
          landmark: string | null
          last_name: string | null
          phone: string | null
          po_box: string | null
          postal_code: string | null
          region: string | null
          role: string
          state: string | null
          updated_at: string | null
          website: string | null
        }
        Insert: {
          address?: string | null
          avatar_url?: string | null
          bio?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          email: string
          first_name?: string | null
          id: string
          landmark?: string | null
          last_name?: string | null
          phone?: string | null
          po_box?: string | null
          postal_code?: string | null
          region?: string | null
          role?: string
          state?: string | null
          updated_at?: string | null
          website?: string | null
        }
        Update: {
          address?: string | null
          avatar_url?: string | null
          bio?: string | null
          city?: string | null
          country?: string | null
          created_at?: string | null
          email?: string
          first_name?: string | null
          id?: string
          landmark?: string | null
          last_name?: string | null
          phone?: string | null
          po_box?: string | null
          postal_code?: string | null
          region?: string | null
          role?: string
          state?: string | null
          updated_at?: string | null
          website?: string | null
        }
        Relationships: []
      }
      receipts: {
        Row: {
          amount: number
          commission: number
          created_at: string | null
          currency: string
          generated_at: string | null
          id: string
          order_id: string
          payout_id: string
          receipt_data: Json | null
          receipt_id: string
          store_id: string
          transaction_id: string | null
          updated_at: string | null
        }
        Insert: {
          amount: number
          commission: number
          created_at?: string | null
          currency?: string
          generated_at?: string | null
          id?: string
          order_id: string
          payout_id: string
          receipt_data?: Json | null
          receipt_id: string
          store_id: string
          transaction_id?: string | null
          updated_at?: string | null
        }
        Update: {
          amount?: number
          commission?: number
          created_at?: string | null
          currency?: string
          generated_at?: string | null
          id?: string
          order_id?: string
          payout_id?: string
          receipt_data?: Json | null
          receipt_id?: string
          store_id?: string
          transaction_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "receipts_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "receipts_payout_id_fkey"
            columns: ["payout_id"]
            isOneToOne: false
            referencedRelation: "payouts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "receipts_store_id_fkey"
            columns: ["store_id"]
            isOneToOne: false
            referencedRelation: "stores"
            referencedColumns: ["id"]
          },
        ]
      }
      reviews: {
        Row: {
          content: string | null
          created_at: string | null
          id: string
          product_id: string | null
          rating: number
          store_id: string
          title: string | null
          updated_at: string | null
          user_id: string
          vendor_id: string | null
        }
        Insert: {
          content?: string | null
          created_at?: string | null
          id?: string
          product_id?: string | null
          rating: number
          store_id: string
          title?: string | null
          updated_at?: string | null
          user_id: string
          vendor_id?: string | null
        }
        Update: {
          content?: string | null
          created_at?: string | null
          id?: string
          product_id?: string | null
          rating?: number
          store_id?: string
          title?: string | null
          updated_at?: string | null
          user_id?: string
          vendor_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "reviews_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_store_id_fkey"
            columns: ["store_id"]
            isOneToOne: false
            referencedRelation: "stores"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_user_id_profiles_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "stores_compatibility"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_vendor_id_fkey"
            columns: ["vendor_id"]
            isOneToOne: false
            referencedRelation: "vendors"
            referencedColumns: ["id"]
          },
        ]
      }
      stores: {
        Row: {
          address: string | null
          city: string | null
          contact_email: string | null
          contact_phone: string | null
          country: string | null
          cover_image: string | null
          created_at: string | null
          delivery_fee: number | null
          delivery_radius_km: number | null
          delivery_time_estimate: string | null
          description: string | null
          featured: boolean
          id: string
          logo: string | null
          name: string
          offers_delivery: boolean | null
          owner_id: string | null
          postal_code: string | null
          rating: number | null
          review_count: number | null
          slug: string
          state: string | null
          status: string
          updated_at: string | null
        }
        Insert: {
          address?: string | null
          city?: string | null
          contact_email?: string | null
          contact_phone?: string | null
          country?: string | null
          cover_image?: string | null
          created_at?: string | null
          delivery_fee?: number | null
          delivery_radius_km?: number | null
          delivery_time_estimate?: string | null
          description?: string | null
          featured?: boolean
          id?: string
          logo?: string | null
          name: string
          offers_delivery?: boolean | null
          owner_id?: string | null
          postal_code?: string | null
          rating?: number | null
          review_count?: number | null
          slug: string
          state?: string | null
          status?: string
          updated_at?: string | null
        }
        Update: {
          address?: string | null
          city?: string | null
          contact_email?: string | null
          contact_phone?: string | null
          country?: string | null
          cover_image?: string | null
          created_at?: string | null
          delivery_fee?: number | null
          delivery_radius_km?: number | null
          delivery_time_estimate?: string | null
          description?: string | null
          featured?: boolean
          id?: string
          logo?: string | null
          name?: string
          offers_delivery?: boolean | null
          owner_id?: string | null
          postal_code?: string | null
          rating?: number | null
          review_count?: number | null
          slug?: string
          state?: string | null
          status?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      system_settings: {
        Row: {
          category: string | null
          description: string | null
          setting_key: string
          setting_value: Json
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          category?: string | null
          description?: string | null
          setting_key: string
          setting_value: Json
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          category?: string | null
          description?: string | null
          setting_key?: string
          setting_value?: Json
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: []
      }
      vendors: {
        Row: {
          commission_rate: number | null
          contact_email: string | null
          contact_phone: string | null
          created_at: string | null
          id: string
          name: string
          notes: string | null
          status: string | null
          updated_at: string | null
        }
        Insert: {
          commission_rate?: number | null
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string | null
          id?: string
          name: string
          notes?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          commission_rate?: number | null
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string | null
          id?: string
          name?: string
          notes?: string | null
          status?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      wishlist: {
        Row: {
          created_at: string | null
          id: string
          product_id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          product_id: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          product_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "wishlist_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      stores_compatibility: {
        Row: {
          active: boolean | null
          address: string | null
          city: string | null
          contact_email: string | null
          contact_phone: string | null
          country: string | null
          cover_image: string | null
          created_at: string | null
          description: string | null
          featured: boolean | null
          id: string | null
          logo: string | null
          name: string | null
          owner_id: string | null
          postal_code: string | null
          rating: number | null
          review_count: number | null
          slug: string | null
          state: string | null
          updated_at: string | null
        }
        Insert: {
          active?: never
          address?: never
          city?: never
          contact_email?: string | null
          contact_phone?: string | null
          country?: never
          cover_image?: never
          created_at?: string | null
          description?: never
          featured?: never
          id?: string | null
          logo?: never
          name?: string | null
          owner_id?: never
          postal_code?: never
          rating?: never
          review_count?: never
          slug?: string | null
          state?: never
          updated_at?: string | null
        }
        Update: {
          active?: never
          address?: never
          city?: never
          contact_email?: string | null
          contact_phone?: string | null
          country?: never
          cover_image?: never
          created_at?: string | null
          description?: never
          featured?: never
          id?: string | null
          logo?: never
          name?: string | null
          owner_id?: never
          postal_code?: never
          rating?: never
          review_count?: never
          slug?: string | null
          state?: never
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      calculate_service_fee: {
        Args: { subtotal: number }
        Returns: number
      }
      generate_payout_schedule: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      generate_pickup_code: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_order_details: {
        Args: { order_id_param: string }
        Returns: {
          id: string
          user_id: string
          status: string
          total: number
          currency: string
          shipping_name: string
          shipping_email: string
          shipping_phone: string
          shipping_address: string
          shipping_city: string
          shipping_state: string
          shipping_country: string
          shipping_postal_code: string
          notes: string
          created_at: string
          updated_at: string
          user_email: string
          item_id: string
          product_id: string
          store_id: string
          quantity: number
          price: number
          item_total: number
          product_name: string
          product_image: string
          store_name: string
        }[]
      }
      get_orders_count: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      get_orders_with_user_emails: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          user_id: string
          user_email: string
          status: string
          total: number
          currency: string
          created_at: string
          updated_at: string
          shipping_name: string
          shipping_email: string
          shipping_phone: string
          shipping_address: string
          shipping_city: string
          shipping_state: string
          shipping_country: string
          shipping_postal_code: string
          notes: string
        }[]
      }
      get_products_count: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      get_profiles_count: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      get_recent_orders: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          user_id: string
          user_email: string
          status: string
          total: number
          currency: string
          created_at: string
          updated_at: string
        }[]
      }
      get_recent_users: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          email: string
          first_name: string
          last_name: string
          role: string
          created_at: string
          updated_at: string
        }[]
      }
      get_stores_count: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      get_system_setting: {
        Args: { key_name: string }
        Returns: Json
      }
      get_total_sales: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      promote_to_admin: {
        Args: { user_email: string }
        Returns: undefined
      }
      promote_to_store_owner: {
        Args: { user_email: string }
        Returns: undefined
      }
      send_notification: {
        Args: {
          p_user_id: string
          p_title: string
          p_message: string
          p_type?: string
          p_category?: string
          p_data?: Json
        }
        Returns: string
      }
      update_system_setting: {
        Args: { key_name: string; new_value: Json; user_id?: string }
        Returns: boolean
      }
    }
    Enums: {
      delivery_method: "pickup" | "delivery"
      order_status:
        | "pending"
        | "paid_clicked"
        | "processing"
        | "completed"
        | "cancelled"
      payment_status: "pending" | "paid_clicked" | "confirmed" | "failed"
      payout_status: "pending" | "processing" | "sent" | "completed" | "failed"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      delivery_method: ["pickup", "delivery"],
      order_status: [
        "pending",
        "paid_clicked",
        "processing",
        "completed",
        "cancelled",
      ],
      payment_status: ["pending", "paid_clicked", "confirmed", "failed"],
      payout_status: ["pending", "processing", "sent", "completed", "failed"],
    },
  },
} as const
