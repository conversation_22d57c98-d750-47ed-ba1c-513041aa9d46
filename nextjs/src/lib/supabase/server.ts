import { createServerClient } from '@supabase/ssr'
import { getCookie, setCookie, deleteCookie } from 'cookies-next'
import { ClientType, SassClient } from "@/lib/supabase/unified";
import { Database } from "./database.types";

export async function createSSRClient() {
    return createServerClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
            cookies: {
                get(name: string) {
                    return getCookie(name)
                },
                set(name: string, value: string, options: any) {
                    setCookie(name, value, options)
                },
                remove(name: string, options: any) {
                    deleteCookie(name, options)
                },
            }
        }
    )
}

export async function createSSRSassClient() {
    const client = await createSSRClient();
    return new SassClient(client, ClientType.SERVER);
}