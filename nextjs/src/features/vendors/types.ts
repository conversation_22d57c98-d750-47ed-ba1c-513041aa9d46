// Vendor system types - replacing the complex stores system

export interface Vendor {
  id: string;
  name: string;
  contact_email?: string;
  contact_phone?: string;
  commission_rate: number; // percentage (e.g., 10.00 for 10%)
  status: VendorStatus;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export type VendorStatus = 'active' | 'suspended';

// API response types
export interface VendorsApiResponse {
  vendors: Vendor[];
  count?: number;
  error?: string;
}

export interface VendorApiResponse {
  vendor: Vendor | null;
  error?: string;
}

// Filter and pagination types
export interface VendorFilterParams {
  status?: VendorStatus;
  search?: string;
  sortBy?: keyof Vendor;
  sortOrder?: 'asc' | 'desc';
}

export interface VendorListParams extends VendorFilterParams {
  page?: number;
  per_page?: number;
}

// Admin types for vendor management
export interface AdminVendor extends Vendor {
  product_count?: number;
  total_sales?: number;
  pending_payouts?: number;
}

export interface CreateVendorParams {
  name: string;
  contact_email?: string;
  contact_phone?: string;
  commission_rate?: number;
  status?: VendorStatus;
  notes?: string;
}

export interface UpdateVendorParams extends Partial<CreateVendorParams> {
  id: string;
}

// Vendor statistics
export interface VendorStats {
  total_products: number;
  total_orders: number;
  total_sales: number;
  pending_payouts: number;
  commission_earned: number;
  recent_orders: any[];
}

// Product with vendor info (replacing store info)
export interface ProductWithVendor {
  id: string;
  name: string;
  slug: string;
  price: number;
  vendor_id: string;
  vendor: {
    id: string;
    name: string;
    status: VendorStatus;
  };
  // ... other product fields
}

// Order item with vendor info
export interface OrderItemWithVendor {
  id: string;
  order_id: string;
  product_id: string;
  vendor_id: string;
  quantity: number;
  price: number;
  total: number;
  vendor: {
    id: string;
    name: string;
  };
}

// Payout with vendor info
export interface PayoutWithVendor {
  id: string;
  vendor_id: string;
  order_id: string;
  amount: number;
  commission: number;
  currency: string;
  payout_status: string;
  vendor: {
    id: string;
    name: string;
    commission_rate: number;
  };
  created_at: string;
  updated_at: string;
}
