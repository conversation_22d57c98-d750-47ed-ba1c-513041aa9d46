import {
  Store,
  StoreFilterParams,
  StoresApiResponse,
  StoreApiResponse
} from './types';
import { createSPASassClient } from '@/lib/supabase/client';

// Client-side API functions
export async function getStoresClient(
  filters: StoreFilterParams = {}
): Promise<StoresApiResponse> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    let query = supabase.from('vendors').select('*');

    // Apply filters
    if (filters.featured !== undefined) {
      query = query.eq('featured', filters.featured);
    }

    if (filters.search) {
      query = query.ilike('name', `%${filters.search}%`);
    }

    if (filters.sortBy) {
      query = query.order(filters.sortBy, { ascending: filters.sortOrder === 'asc' });
    } else {
      query = query.order('name');
    }

    const { data, error } = await query;

    if (error) {
      throw error;
    }

    return { stores: data as Store[] || [] };
  } catch (error) {
    console.error('Error fetching stores:', error);
    return {
      stores: [],
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

export async function getStoreBySlugClient(slug: string): Promise<StoreApiResponse> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { data: storeData, error: storeError } = await supabase
      .from('vendors')
      .select('*')
      .eq('slug', slug)
      .single();

    if (storeError && storeError.code !== 'PGRST116') {
      throw storeError;
    }

    if (storeData) {
      // Get products for this vendor
      const { data: productsData, error: productsError } = await supabase
        .from('products')
        .select(`
          *,
          images:product_images(*)
        `)
        .eq('vendor_id', storeData.id)
        .limit(12);

      if (productsError) throw productsError;

      // Transform products data to match the expected Product type
      const transformedProducts = (productsData || []).map(product => ({
        id: product.id,
        name: product.name,
        slug: product.slug,
        description: product.description,
        price: product.price,
        compareAtPrice: product.compare_at_price,
        currency: product.currency || 'GMD',
        images: (product.images || []).map((img: any) => ({
          id: img.id,
          productId: img.product_id,
          url: img.url,
          alt: img.alt || product.name,
          position: img.position
        })),
        categoryId: product.category_id,
        vendorId: product.vendor_id,
        featured: product.featured,
        trending: product.trending || false,
        inStock: product.in_stock !== false,
        rating: product.rating || 0,
        reviewCount: product.review_count || 0,
        createdAt: product.created_at,
        updatedAt: product.updated_at || product.created_at
      }));

      return {
        store: storeData as Store,
        products: transformedProducts
      };
    }

    return {
      store: null,
      error: 'Store not found'
    };
  } catch (error) {
    console.error(`Error fetching store with slug ${slug}:`, error);
    return {
      store: null,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

export async function getFeaturedStoresClient(limit = 4): Promise<StoresApiResponse> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    const { data, error } = await supabase
      .from('vendors')
      .select('*')
      .eq('status', 'active')
      .order('name')
      .limit(limit);

    if (error) {
      throw error;
    }

    return { stores: data as Store[] || [] };
  } catch (error) {
    console.error('Error fetching featured stores:', error);
    return {
      stores: [],
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}
