import { createServerClient } from '@/lib/supabase/server';
import {
  Store,
  StoreFilterParams
} from './types';

// Server-side API functions
export async function getStores(filters: StoreFilterParams = {}): Promise<Store[]> {
  try {
    const supabase = await createServerClient();
    let query = supabase.from('vendors').select('*');

    if (filters.featured !== undefined) {
      query = query.eq('featured', filters.featured);
    }

    if (filters.search) {
      query = query.ilike('name', `%${filters.search}%`);
    }

    if (filters.sortBy) {
      query = query.order(filters.sortBy, { ascending: filters.sortOrder === 'asc' });
    } else {
      query = query.order('name');
    }

    const { data, error } = await query;

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching stores:', error);
    return [];
  }
}

export async function getStoreBySlug(slug: string): Promise<Store | null> {
  try {
    const supabase = await createServerClient();
    const { data, error } = await supabase
      .from('vendors')
      .select('*')
      .eq('slug', slug)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error(`Error fetching store with slug ${slug}:`, error);
    return null;
  }
}

export async function getFeaturedStores(limit = 4): Promise<Store[]> {
  try {
    const supabase = await createServerClient();
    const { data, error } = await supabase
      .from('vendors')
      .select('*')
      .eq('status', 'active')
      .order('name')
      .limit(limit);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching featured stores:', error);
    return [];
  }
}
