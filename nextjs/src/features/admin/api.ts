import { createServerAdminClient } from '@/lib/supabase/serverAdminClient';
import {
  AdminUser,
  AdminProduct,
  AdminOrder,
  AdminPayment,
  AdminPayout,
  AdminDeal,
  DashboardStats,
  UserListParams,
  ProductListParams,
  OrderListParams,
  PaymentListParams,
  PayoutListParams,
  DealListParams,
  UpdateUserParams,
  UpdateProductParams,
  CreateProductParams,
  UpdateOrderParams,
  UpdatePaymentParams,
  UpdatePayoutParams,
  CreateDealParams,
  UpdateDealParams,
  Category,
  HeroSlide,
  SlideListParams,
  CreateSlideParams,
  UpdateSlideParams,
  UserRole
} from './types';
import { AdminVendor } from '../vendors/types';
import { createSPASassClient } from '@/lib/supabase/client';
import { Database } from '@/lib/supabase/database.types';
import { SupabaseClient } from '@supabase/supabase-js';
import { NotificationService } from '@/lib/services/notification';

type TypedSupabaseClient = SupabaseClient<Database>;

// Check if user is admin
export async function isUserAdmin(userId: string): Promise<boolean> {
  try {
    const supabase = await createServerAdminClient() as TypedSupabaseClient;
    const { data, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    if (error) throw error;
    return data?.role === 'admin';
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

// Get current user's role
export async function getCurrentUserRole(userId: string): Promise<UserRole> {
  try {
    const supabase = await createServerAdminClient() as TypedSupabaseClient;
    const { data, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single();

    if (error) throw error;
    return (data?.role as UserRole) || 'user';
  } catch (error) {
    console.error('Error getting user role:', error);
    return 'user';
  }
}

// Get current user's vendor information (simplified - no ownership concept)
export async function getCurrentUserVendor(): Promise<{ vendor: AdminVendor | null, error: string | null }> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient() as TypedSupabaseClient;

    // Get the current user
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { vendor: null, error: 'User not authenticated' };
    }

    // For now, return null as vendors are admin-managed only
    // This function exists for compatibility but vendors don't have owners
    return { vendor: null, error: 'Vendors are managed by administrators only' };

  } catch (error) {
    console.error('Error getting current user vendor:', error);
    return {
      vendor: null,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

// Get all vendors (admin function)
export async function getVendors(): Promise<{ vendors: AdminVendor[], error: string | null }> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient() as TypedSupabaseClient;

    // Get all vendors
    const { data: vendors, error } = await supabase
      .from('vendors')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('getVendors: Error fetching vendors:', error);
      return { vendors: [], error: error.message };
    }

    console.log('getVendors: Found vendors:', vendors?.length || 0);

    return { vendors: (vendors || []) as AdminVendor[], error: null };
  } catch (error) {
    console.error('Error getting vendors:', error);
    return {
      vendors: [],
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

// Dashboard stats
export async function getDashboardStats(storeId?: string): Promise<DashboardStats> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Fallback to direct queries if RPC functions fail
    const getProfilesCount = async () => {
      try {
        const { count } = await supabase.from('profiles').select('*', { count: 'exact', head: true });
        return count || 0;
      } catch (error) {
        console.warn('Error getting profiles count:', error);
        return 0;
      }
    };

    const getVendorsCount = async () => {
      try {
        const { count } = await supabase.from('vendors').select('*', { count: 'exact', head: true });
        return count || 0;
      } catch (error) {
        console.warn('Error getting vendors count:', error);
        return 0;
      }
    };

    const getProductsCount = async () => {
      try {
        let query = supabase.from('products').select('*', { count: 'exact', head: true });
        if (storeId) {
          query = query.eq('store_id', storeId);
        }
        const { count } = await query;
        return count || 0;
      } catch (error) {
        console.warn('Error getting products count:', error);
        return 0;
      }
    };

    const getOrdersCount = async () => {
      try {
        if (storeId) {
          // For store owners, count orders that have items from their store
          const { data: orderItems } = await supabase
            .from('order_items')
            .select('order_id')
            .eq('store_id', storeId);

          if (orderItems) {
            const uniqueOrderIds = new Set((orderItems as Database['public']['Tables']['order_items']['Row'][])?.map(item => item.order_id) || []);
            return uniqueOrderIds.size;
          }
          return 0;
        } else {
          // For admins, count all orders
          const { count } = await supabase.from('orders').select('*', { count: 'exact', head: true });
          return count || 0;
        }
      } catch (error) {
        console.warn('Error getting orders count:', error);
        return 0;
      }
    };

    const getTotalSales = async () => {
      try {
        if (storeId) {
          // For store owners, sum sales from their store's order items
          const { data } = await supabase
            .from('order_items')
            .select('price, quantity')
            .eq('store_id', storeId);

          if (data && data.length > 0) {
            return (data as Database['public']['Tables']['order_items']['Row'][]).reduce((sum, item) => sum + (parseFloat(item.price) * item.quantity || 0), 0);
          }
          return 0;
        } else {
          // For admins, sum all order totals
          const { data } = await supabase.from('orders').select('total');
          if (data && data.length > 0) {
            return (data as Database['public']['Tables']['orders']['Row'][]).reduce((sum, order) => sum + (parseFloat(order.total) || 0), 0);
          }
          return 0;
        }
      } catch (error) {
        console.warn('Error getting total sales:', error);
        return 0;
      }
    };

    const getRecentOrders = async () => {
      try {
        if (storeId) {
          // For store owners, get orders that have items from their store
          const { data: orderItems } = await supabase
            .from('order_items')
            .select('order_id')
            .eq('store_id', storeId);

          if (orderItems && orderItems.length > 0) {
            const orderIds = (orderItems as Database['public']['Tables']['order_items']['Row'][]).map(item => item.order_id);
            const { data } = await supabase
              .from('orders')
              .select('*, profiles(email)')
              .in('id', orderIds)
              .order('created_at', { ascending: false })
              .limit(5);

            return data || [];
          }
          return [];
        } else {
          // For admins, get all recent orders
          const { data } = await supabase
            .from('orders')
            .select('*, profiles(email)')
            .order('created_at', { ascending: false })
            .limit(5);

          return data || [];
        }
      } catch (error) {
        console.warn('Error getting recent orders:', error);
        return [];
      }
    };

    const getRecentUsers = async () => {
      try {
        const { data } = await supabase
          .from('profiles')
          .select('*')
          .order('created_at', { ascending: false })
          .limit(5);

        return data || [];
      } catch (error) {
        console.warn('Error getting recent users:', error);
        return [];
      }
    };

    // Get counts using direct queries
    const [
      usersCount,
      vendorsCount,
      productsCount,
      ordersCount,
      totalSales,
      recentOrders,
      recentUsers
    ] = await Promise.all([
      getProfilesCount(),
      getVendorsCount(),
      getProductsCount(),
      getOrdersCount(),
      getTotalSales(),
      getRecentOrders(),
      getRecentUsers()
    ]);

    // Format recent users data
    const formattedRecentUsers = Array.isArray(recentUsers) ? 
      (recentUsers as Database['public']['Tables']['profiles']['Row'][]).map((user) => ({
        id: user.id,
        email: user.email,
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        role: user.role as UserRole || 'user',
        created_at: user.created_at || '',
        updated_at: user.updated_at || ''
      })) : [];

    // Format recent orders data
    const formattedRecentOrders = Array.isArray(recentOrders)
      ? (recentOrders as Database["public"]["Tables"]["orders"]["Row"][]).map(
          (order) => ({
            id: order.id,
            user_id: order.user_id,
            status: order.status || "pending",
            total: order.total || 0,
            currency: order.currency || "GMD",
            created_at: order.created_at || "",
            updated_at: order.updated_at || "",
          })
        )
      : [];

    // Calculate Finder earnings (assuming 5% commission)
    const finderEarnings = (totalSales || 0) * 0.05;

    return {
      users_count: usersCount || 0,
      vendors_count: vendorsCount || 0,
      products_count: productsCount || 0,
      orders_count: ordersCount || 0,
      total_sales: totalSales || 0,
      finder_earnings: finderEarnings,
      recent_orders: formattedRecentOrders as AdminOrder[],
      recent_users: formattedRecentUsers as AdminUser[]
    };
  } catch (error) {
    console.error('Error getting dashboard stats:', error);
    return {
      users_count: 0,
      vendors_count: 0,
      products_count: 0,
      orders_count: 0,
      total_sales: 0,
      finder_earnings: 0,
      recent_orders: [],
      recent_users: []
    };
  }
}

// User management
export async function getUsers(params: UserListParams = {}): Promise<{ users: AdminUser[], count: number }> {
  try {
    const {
      page = 1,
      per_page = 10,
      search = '',
      role,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = params;

    const supabase = await createServerAdminClient();

    // Build query
    let query = supabase.from('profiles' as any).select('*', { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.or(`email.ilike.%${search}%,first_name.ilike.%${search}%,last_name.ilike.%${search}%`);
    }

    if (role) {
      query = query.eq('role', role);
    }

    // Apply pagination
    const from = (page - 1) * per_page;
    const to = from + per_page - 1;

    // Get results
    const { data, error, count } = await query
      .order(sort_by, { ascending: sort_order === 'asc' })
      .range(from, to);

    if (error) throw error;

    return {
      users: data as any as AdminUser[],
      count: count || 0
    };
  } catch (error) {
    console.error('Error getting users:', error);
    return { users: [], count: 0 };
  }
}

export async function getUser(id: string): Promise<AdminUser | null> {
  try {
    const supabase = await createServerAdminClient();
    const { data, error } = await supabase
      .from('profiles' as any)
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data as any as AdminUser;
  } catch (error) {
    console.error('Error getting user:', error);
    return null;
  }
}

export async function updateUser(id: string, params: UpdateUserParams): Promise<AdminUser | null> {
  try {
    const supabase = await createServerAdminClient();
    const { data, error } = await supabase
      .from('profiles' as any)
      .update({
        ...params,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data as unknown as AdminUser;
  } catch (error) {
    console.error('Error updating user:', error);
    return null;
  }
}

// Product management
export async function getProduct(id: string): Promise<AdminProduct | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { data, error } = await supabase
      .from('products' as any)
      .select('*, categories(name), stores(name)')
      .eq('id', id)
      .single();

    if (error) throw error;

    return {
      ...(data as any),
      category_name: (data as any).categories?.name,
      store_name: (data as any).stores?.name,
      categories: undefined,
      stores: undefined
    } as AdminProduct;
  } catch (error) {
    console.error('Error getting product:', error);
    return null;
  }
}

export async function updateProduct(id: string, params: UpdateProductParams): Promise<AdminProduct | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { data, error } = await supabase
      .from('products')
      .update({
        ...params,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data as any as AdminProduct;
  } catch (error) {
    console.error('Error updating product:', error);
    return null;
  }
}

export async function createProduct(params: CreateProductParams): Promise<AdminProduct | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { data, error } = await supabase
      .from('products')
      .insert({
        ...params,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data as any as AdminProduct;
  } catch (error) {
    console.error('Error creating product:', error);
    return null;
  }
}

export async function getCategories(params: CategoryListParams = {}): Promise<{ categories: Category[], count: number }> {
  try {
    const {
      page = 1,
      per_page = 10,
      search = '',
      featured,
      parent_id,
      sort_by = 'name',
      sort_order = 'asc'
    } = params;

    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Build query with product count and parent category info
    let query = supabase
      .from('categories' as any)
      .select(`
        *,
        products(count),
        parent:parent_id(id, name, slug)
      `, { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    if (featured !== undefined) {
      query = query.eq('featured', featured);
    }

    if (parent_id !== undefined) {
      if (parent_id === null || parent_id === '') {
        query = query.is('parent_id', null);
      } else {
        query = query.eq('parent_id', parent_id);
      }
    }

    // Apply sorting - sort by parent first, then by name
    if (sort_by === 'name') {
      query = query.order('parent_id', { ascending: true, nullsFirst: true })
                   .order('name', { ascending: sort_order === 'asc' });
    } else {
      query = query.order(sort_by, { ascending: sort_order === 'asc' });
    }

    // Apply pagination
    const from = (page - 1) * per_page;
    const to = from + per_page - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) throw error;

    // Transform data to include product count and parent info
    const categories = data?.map((category: any) => ({
      ...category,
      product_count: category.products?.[0]?.count || 0,
      parent: category.parent,
      products: undefined
    })) || [];

    return { categories: categories as Category[], count: count || 0 };
  } catch (error) {
    console.error('Error getting categories:', error);
    return { categories: [], count: 0 };
  }
}

export async function getCategory(id: string): Promise<Category | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { data, error } = await supabase
      .from('categories')
      .select(`
        *,
        products(count)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;

    return {
      ...data,
      product_count: data.products?.[0]?.count || 0,
      products: undefined
    } as Category;
  } catch (error) {
    console.error('Error getting category:', error);
    return null;
  }
}

export async function createCategory(params: CreateCategoryParams): Promise<Category | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Clean up the params to handle null values properly
    const cleanParams = { ...params };
    if (cleanParams.parent_id === '') {
      cleanParams.parent_id = null;
    }

    const { data, error } = await supabase
      .from('categories' as any)
      .insert({
        ...cleanParams,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data as Category;
  } catch (error) {
    console.error('Error creating category:', error);
    return null;
  }
}

export async function updateCategory(id: string, params: UpdateCategoryParams): Promise<Category | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Clean up the params to handle null values properly
    const cleanParams = { ...params };
    if (cleanParams.parent_id === '') {
      cleanParams.parent_id = null;
    }

    const { data, error } = await supabase
      .from('categories' as any)
      .update({
        ...cleanParams,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data as Category;
  } catch (error) {
    console.error('Error updating category:', error);
    return null;
  }
}

export async function deleteCategory(id: string): Promise<boolean> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Check if category has products
    const { count } = await supabase
      .from('products' as any)
      .select('*', { count: 'exact', head: true })
      .eq('category_id', id);

    if (count && count > 0) {
      throw new Error('Cannot delete category with existing products');
    }

    const { error } = await supabase
      .from('categories' as any)
      .delete()
      .eq('id', id);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error deleting category:', error);
    return false;
  }
}

// Get simple categories list for dropdowns
export async function getCategoriesSimple(): Promise<Category[]> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { data, error } = await supabase
      .from('categories' as any)
      .select('id, name, slug')
      .is('parent_id', null) // Only get top-level categories for parent selection
      .order('name');

    if (error) throw error;
    return data as Category[];
  } catch (error) {
    console.error('Error getting simple categories:', error);
    return [];
  }
}

export async function getProducts(params: ProductListParams = {}): Promise<{ products: AdminProduct[], count: number }> {
  try {
    const {
      page = 1,
      per_page = 10,
      search = '',
      category_id,
      store_id,
      featured,
      trending,
      in_stock,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = params;

    console.log('getProducts API: Received params:', params);


    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Build query
    let query = supabase.from('products' as any)
      .select('*, categories(name), stores(name)', { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    if (category_id) {
      query = query.eq('category_id', category_id);
    }

    if (store_id) {
      console.log('getProducts API: Applying store_id filter:', store_id);
      query = query.eq('store_id', store_id);
    }

    if (featured !== undefined) {
      query = query.eq('featured', featured);
    }

    if (trending !== undefined) {
      query = query.eq('trending', trending);
    }

    if (in_stock !== undefined) {
      query = query.eq('in_stock', in_stock);
    }

    // Apply pagination
    const from = (page - 1) * per_page;
    const to = from + per_page - 1;

    // Get results
    const { data, error, count } = await query
      .order(sort_by, { ascending: sort_order === 'asc' })
      .range(from, to);

    if (error) throw error;

    // Transform data to include category and store names
    const products = (data as Database['public']['Tables']['products']['Row'][]).map(product => ({
      ...product,
      category_name: product.categories?.name,
      store_name: product.stores?.name,
      categories: undefined,
      stores: undefined
    }));

    return {
      products: products as AdminProduct[],
      count: count || 0
    };
  } catch (error) {
    console.error('Error getting products:', error);
    return { products: [], count: 0 };
  }
}

// Order management
export async function getOrder(id: string): Promise<AdminOrder | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Get the order data
    const { data: orderData, error: orderError } = await supabase
      .from('orders' as any)
      .select('*')
      .eq('id', id)
      .single();

    if (orderError) {
      console.error('Error fetching order:', orderError);
      return null;
    }

    if (!orderData) {
      return null;
    }

    // Get user email from profiles
    const { data: profileData } = await supabase
      .from('profiles' as any)
      .select('email')
      .eq('id', orderData.user_id)
      .single();

    // Get order items with product and store details
    const { data: itemsData } = await supabase
      .from('order_items' as any)
      .select(
        `
        *,
        products(name),
        stores(name)
        `
      )
      .eq('order_id', id);

    // Transform items data
    const items = itemsData?.map((item: any) => ({
      id: item.id,
      product_id: item.product_id,
      store_id: item.store_id,
      quantity: item.quantity,
      price: item.price,
      total: item.total,
      product: {
        name: item.products?.name || 'Unknown Product',
        images: [] // We'll need to fetch product images separately if needed
      },
      store: {
        name: item.stores?.name || 'Unknown Store'
      }
    })) || [];

    // Return the order with items
    return {
      id: orderData.id,
      user_id: orderData.user_id,
      status: orderData.status,
      total: orderData.total,
      currency: orderData.currency || 'GMD',
      created_at: orderData.created_at,
      updated_at: orderData.updated_at,
      user_email: profileData?.email,
      shipping_name: orderData.shipping_name,
      shipping_email: orderData.shipping_email,
      shipping_phone: orderData.shipping_phone,
      shipping_address: orderData.shipping_address,
      shipping_city: orderData.shipping_city,
      shipping_state: orderData.shipping_state,
      shipping_country: orderData.shipping_country,
      shipping_postal_code: orderData.shipping_postal_code,
      notes: orderData.notes,
      items_count: items.length,
      items: items
    };
  } catch (error) {
    console.error('Error getting order:', error);
    return null;
  }
}

export async function getOrders(params: OrderListParams = {}): Promise<{ orders: AdminOrder[], count: number }> {
  console.log('getOrders: Function called with params:', params);
  try {
    const {
      page = 1,
      per_page = 10,
      search = '',
      status,
      user_id,
      store_id,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = params;

    console.log('getOrders: Starting with params:', params);
    const startTime = Date.now();

    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();


    // Check if current user is a store owner and auto-filter for their stores
    const { data: { user } } = await supabase.auth.getUser();
    console.log('getOrders: Got user in', Date.now() - startTime, 'ms');
    let userStoreIds: string[] = [];
    let isStoreOwner = false;

    if (user) {
      // Get user's role
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();
      console.log('getOrders: Got profile in', Date.now() - startTime, 'ms');

      console.log('getOrders: User profile:', profile);

      // If user is a store owner, get their store IDs for filtering
      if (profile?.role === 'store_owner') {
        isStoreOwner = true;
        const { stores, error: storesError } = await getCurrentUserStores();
        console.log('getOrders: Store owner stores:', stores, 'error:', storesError);

        if (stores && stores.length > 0) {
          userStoreIds = stores.map(store => (store as any).id);
          console.log('getOrders: Store IDs for filtering:', userStoreIds);
        } else {
          console.log('getOrders: Store owner has no stores');
        }
      }
    }

    // For store owners, we need to filter orders that have items from their stores
    let orderIds: string[] = [];
    if (isStoreOwner && userStoreIds.length > 0) {
      // Get all order IDs that have items from user's stores
      const { data: orderItems, error: orderItemsError } = await supabase
        .from('order_items' as any)
        .select('order_id')
        .in('store_id', userStoreIds);

      console.log('getOrders: Order items query result:', orderItems, 'error:', orderItemsError);

      if (orderItems && orderItems.length > 0) {
        orderIds = [...new Set(orderItems.map(item => item.order_id))];
        console.log('getOrders: Found order IDs:', orderIds);
      } else {
        console.log('getOrders: No order items found for store owner stores');
        // Let's also check if there are any orders at all
        const { data: allOrders, error: allOrdersError } = await supabase
          .from('orders' as any)
          .select('id, status, total')
          .limit(5);
        console.log('getOrders: All orders in database:', allOrders, 'error:', allOrdersError);

        // And check if there are any order_items at all
        const { data: allOrderItems, error: allOrderItemsError } = await supabase
          .from('order_items' as any)
          .select('id, order_id, store_id')
          .limit(5);
        console.log('getOrders: All order items in database:', allOrderItems, 'error:', allOrderItemsError);

        // For debugging, let's not return empty yet - let's see what happens
        // return { orders: [], count: 0 };
      }
    } else if (isStoreOwner && userStoreIds.length === 0) {
      console.log('getOrders: Store owner has no stores, returning empty');
      return { orders: [], count: 0 };
    }

    // Build query for orders
    let query = supabase.from('orders' as any)
      .select('*, order_items(id)', { count: 'exact' });

    // Apply store owner filtering if applicable
    if (isStoreOwner && orderIds.length > 0) {
      console.log('getOrders: Applying store owner filter with order IDs:', orderIds);
      query = query.in('id', orderIds);
    } else if (isStoreOwner && orderIds.length === 0) {
      console.log('getOrders: Store owner has no orders, showing all orders for debugging');
      // For debugging, let's show all orders temporarily - REMOVE THIS LATER
      // query = query.eq('id', 'non-existent-id'); // This would return empty results
    }

    // Apply other filters
    if (search) {
      query = query.or(`id.ilike.%${search}%,shipping_email.ilike.%${search}%,shipping_name.ilike.%${search}%`);
    }

    if (status) {
      query = query.eq('status', status);
    }

    if (user_id) {
      query = query.eq('user_id', user_id);
    }

    // Apply pagination
    const from = (page - 1) * per_page;
    const to = from + per_page - 1;

    // Get results
    console.log('getOrders: About to execute main query at', Date.now() - startTime, 'ms');
    const { data, error, count } = await query
      .order(sort_by, { ascending: sort_order === 'asc' })
      .range(from, to);
    console.log('getOrders: Main query completed in', Date.now() - startTime, 'ms');

    if (error) throw error;

    // Additional store_id filter (for admin use)
    let filteredOrders = data;
    if (store_id && userStoreIds.length === 0) { // Only apply if not already filtered by store owner
      // Get all order IDs that have items from this store
      const { data: orderItems } = await supabase
        .from('order_items' as any)
        .select('order_id')
        .eq('store_id', store_id);

      if (orderItems) {
        const orderIdsWithStore = new Set(orderItems.map((item: any) => item.order_id));
        filteredOrders = data.filter((order: any) => orderIdsWithStore.has(order.id));
      }
    }

    // Transform data to include items count
    const orders = (filteredOrders as Database['public']['Tables']['orders']['Row'][]).map((order: any) => ({
      ...order,
      items_count: order.order_items?.length || 0,
      order_items: undefined
    }));

    return {
      orders: orders as AdminOrder[],
      count: store_id ? orders.length : (count || 0)
    };
  } catch (error) {
    console.error('getOrders: ERROR occurred:', error);
    console.error('getOrders: Error details:', JSON.stringify(error, null, 2));
    return { orders: [], count: 0 };
  }
}

export async function updateOrder(id: string, params: UpdateOrderParams): Promise<AdminOrder | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // First get the current order to check if status is changing
    const { data: existingOrder, error: fetchError } = await supabase
      .from('orders' as any)
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) throw fetchError;

    // Update the order
    const { data, error } = await supabase
      .from('orders' as any)
      .update({
        ...params,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    // If status is being changed, create notifications
    if (params.status && params.status !== (existingOrder as any).status) {
      try {
        // Create admin notification
        await NotificationService.createOrderStatusNotification(
          id,
          params.status
        );

        // Create customer notification
        if ((existingOrder as any).user_id) {
          await NotificationService.createCustomerOrderNotification(
            (existingOrder as any).user_id,
            id,
            params.status
          );
        }
      } catch (notificationError) {
        console.error('Error creating order status notification:', notificationError);
        // Continue even if notification creation fails
      }
    }

    return data as any as AdminOrder;
  } catch (error) {
    console.error('Error updating order:', error);
    return null;
  }
}

// Start order review workflow
export async function startOrderReview(orderId: string): Promise<boolean> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Update order status to under_review
    const { data: order, error: orderError } = await supabase
      .from('orders' as any)
      .update({
        status: 'under_review',
        updated_at: new Date().toISOString()
      })
      .eq('id', orderId)
      .select('user_id')
      .single();

    if (orderError) throw orderError;

    // Get all stores involved in this order
    const { data: orderItems, error: itemsError } = await supabase
      .from('order_items' as any)
      .select('store_id, price, quantity, total, stores(id, name)')
      .eq('order_id', orderId);

    if (itemsError) throw itemsError;

    // Create or update order_store_items for each store
    const storeIds = [...new Set(orderItems.map((item: any) => item.store_id))];

    for (const storeId of storeIds) {
      // First check if order_store_items exists for this order and store
      const { data: existingStoreItem, error: checkError } = await supabase
        .from('order_store_items' as any)
        .select('id')
        .eq('order_id', orderId)
        .eq('store_id', storeId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        console.error('Error checking order store item:', checkError);
        continue;
      }

      if (!existingStoreItem) {
        // Create order_store_items if it doesn't exist
        // Calculate total amount for this store
        const storeItems = orderItems.filter((item: any) => item.store_id === storeId);
        const totalAmount = storeItems.reduce((sum: number, item: any) => {
          // Use the total field if available, otherwise calculate from price * quantity
          return sum + ((item as any).total || ((item as any).price || 0) * ((item as any).quantity || 1));
        }, 0);

        const { error: createError } = await supabase
          .from('order_store_items' as any)
          .insert({
            order_id: orderId,
            store_id: storeId,
            total_amount: totalAmount,
            currency: 'GMD',
            status: 'awaiting_store_confirmation',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          } as any);

        if (createError) {
          console.error('Error creating order store item:', createError);
          continue;
        }
      } else {
        // Update existing order_store_items status to awaiting_store_confirmation
        const { error: storeItemError } = await supabase
          .from('order_store_items' as any)
          .update({
            status: 'awaiting_store_confirmation',
            updated_at: new Date().toISOString()
          })
          .eq('order_id', orderId)
          .eq('store_id', storeId);

        if (storeItemError) {
          console.error('Error updating order store item:', storeItemError);
          continue;
        }
      }

      // Notify store owner
      await NotificationService.createStoreOrderReviewNotification(orderId, storeId);
    }

    // Notify customer that order is under review
    if ((order as any).user_id) {
      await NotificationService.createCustomerOrderNotification(
        (order as any).user_id,
        orderId,
        'under_review'
      );
    }

    return true;
  } catch (error) {
    console.error('Error starting order review:', error);
    return false;
  }
}

// Confirm order by store owner
export async function confirmOrderByStore(orderId: string, storeId: string): Promise<boolean> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    console.log('confirmOrderByStore: Confirming order:', orderId, 'for store:', storeId);

    // First, verify that this store has items in this order
    const { data: orderItems, error: itemsError } = await supabase
      .from('order_items' as any)
      .select('id')
      .eq('order_id', orderId)
      .eq('store_id', storeId);

    if (itemsError) {
      console.error('Error checking order items:', itemsError);
      throw itemsError;
    }

    if (!orderItems || orderItems.length === 0) {
      console.error('No items found for this store in this order');
      return false;
    }

    console.log('confirmOrderByStore: Found', orderItems.length, 'items for this store');

    // Try to update order_store_items if it exists
    const { data: existingStoreItem, error: checkError } = await supabase
      .from('order_store_items' as any)
      .select('id')
      .eq('order_id', orderId)
      .eq('store_id', storeId)
      .single();

    if (!checkError && existingStoreItem) {
      // Update existing order_store_items
      const { error: storeItemError } = await supabase
        .from('order_store_items' as any)
        .update({
          status: 'accepted',
          accepted_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('order_id', orderId)
        .eq('store_id', storeId);

      if (storeItemError) {
        console.error('Error updating order_store_items:', storeItemError);
        // Don't throw error, continue with order status update
      }
    } else {
      // Create order_store_items entry if it doesn't exist
      const { data: items, error: itemsError } = await supabase
        .from('order_items' as any)
        .select('total')
        .eq('order_id', orderId)
        .eq('store_id', storeId);

      if (!itemsError && items) {
        const totalAmount = items.reduce((sum, item) => sum + (item.total || 0), 0);

        const { error: createError } = await supabase
          .from('order_store_items' as any)
          .insert({
            order_id: orderId,
            store_id: storeId,
            total_amount: totalAmount,
            currency: 'GMD',
            status: 'accepted',
            accepted_at: new Date().toISOString(),
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          } as any);

        if (createError) {
          console.error('Error creating order_store_items:', createError);
          // Don't throw error, continue with order status update
        }
      }
    }

    // Update main order status to accepted_by_store
    const { error: orderError } = await supabase
      .from('orders' as any)
      .update({
        status: 'accepted_by_store',
        updated_at: new Date().toISOString()
      })
      .eq('id', orderId);

    if (orderError) {
      console.error('Error updating order status:', orderError);
      throw orderError;
    }

    console.log('confirmOrderByStore: Successfully updated order status');

    // Get store name for notification
    const { data: store, error: storeError } = await supabase
      .from('stores' as any)
      .select('name')
      .eq('id', storeId)
      .single();

    if (!storeError && store) {
      // Notify admins that store confirmed
      await NotificationService.createStoreOrderConfirmationNotification(
        orderId,
        storeId,
        (store as any).name
      );
    }

    return true;
  } catch (error) {
    console.error('Error confirming order by store:', error);
    return false;
  }
}

// Enhanced payment confirmation with inventory management
export async function confirmPayment(orderId: string): Promise<boolean> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Get current user for confirmation record
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    // Get payment record for this order
    const { data: payment, error: paymentError } = await supabase
      .from('payments' as any)
      .select('id')
      .eq('order_id', orderId)
      .single();

    if (paymentError) throw paymentError;

    // Update payment status to confirmed
    const { error: updatePaymentError } = await supabase
      .from('payments' as any)
      .update({
        payment_status: 'confirmed',
        updated_at: new Date().toISOString()
      })
      .eq('id', payment.id);

    if (updatePaymentError) throw updatePaymentError;

    // Create confirmed payment record (this will trigger inventory update)
    const { error: confirmedPaymentError } = await supabase
      .from('confirmed_payments' as any)
      .insert({
        order_id: orderId,
        payment_id: payment.id,
        confirmed_by: user.id,
        confirmed_at: new Date().toISOString()
      });

    if (confirmedPaymentError) throw confirmedPaymentError;

    // Update order status to processing
    const { data: order, error: orderError } = await supabase
      .from('orders' as any)
      .update({
        status: 'processing',
        updated_at: new Date().toISOString()
      })
      .eq('id', orderId)
      .select('user_id, total, currency')
      .single();

    if (orderError) throw orderError;

    // Get all order items to create payout records
    const { data: orderItems, error: itemsError } = await supabase
      .from('order_items' as any)
      .select('store_id, quantity, price, total, products(name), stores(name)')
      .eq('order_id', orderId);

    if (itemsError) throw itemsError;

    // Create payout records for each store
    const finderCommissionRate = 0.05; // 5% commission
    const payoutPromises = orderItems.map(async (item: any) => {
      const itemTotal = parseFloat(item.total) || (parseFloat(item.price) * item.quantity);
      const finderCommission = itemTotal * finderCommissionRate;
      const storeAmount = itemTotal - finderCommission;

      return supabase
        .from('payouts' as any)
        .insert({
          store_id: item.store_id,
          order_id: orderId,
          amount: storeAmount,
          commission: finderCommission,
          currency: (order as any).currency || 'GMD',
          payout_status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
    });

    const payoutResults = await Promise.all(payoutPromises);
    const payoutErrors = payoutResults.filter(result => result.error);

    if (payoutErrors.length > 0) {
      console.error('Some payout records failed to create:', payoutErrors);
    }

    // Notify each store owner about payment confirmation
    const uniqueStores = [...new Set(orderItems.map((item: any) => item.store_id))];
    for (const storeId of uniqueStores) {
      const storeItems = orderItems.filter((item: any) => item.store_id === storeId);
      const storeTotal = storeItems.reduce((sum: number, item: any) => {
        return sum + (parseFloat(item.total) || (parseFloat(item.price) * item.quantity));
      }, 0);

      await NotificationService.createPaymentConfirmationNotification(
        orderId,
        storeId,
        storeTotal,
        (order as any).currency || 'GMD'
      );
    }

    // Notify customer that order is being processed
    if ((order as any).user_id) {
      await NotificationService.createCustomerOrderNotification(
        (order as any).user_id,
        orderId,
        'processing'
      );
    }

    return true;
  } catch (error) {
    console.error('Error confirming payment:', error);
    return false;
  }
}

// Payment management
export async function getPayments(params: PaymentListParams = {}): Promise<{ payments: AdminPayment[], count: number }> {
  try {
    const {
      page = 1,
      per_page = 10,
      search = '',
      payment_status,
      payment_method,
      order_id,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = params;

    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Build query
    let query = supabase.from('payments' as any)
      .select('*', { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.or(`id.ilike.%${search}%,transaction_id.ilike.%${search}%`);
    }

    if (payment_status) {
      query = query.eq('payment_status', payment_status);
    }

    if (payment_method) {
      query = query.eq('payment_method', payment_method);
    }

    if (order_id) {
      query = query.eq('order_id', order_id);
    }

    // Apply pagination
    const from = (page - 1) * per_page;
    const to = from + per_page - 1;

    // Get results
    const { data, error, count } = await query
      .order(sort_by, { ascending: sort_order === 'asc' })
      .range(from, to);

    if (error) throw error;

    return {
      payments: data as any as AdminPayment[],
      count: count || 0
    };
  } catch (error) {
    console.error('Error getting payments:', error);
    return { payments: [], count: 0 };
  }
}

export async function getPayment(id: string): Promise<AdminPayment | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { data, error } = await supabase
      .from('payments' as any)
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data as any as AdminPayment;
  } catch (error) {
    console.error('Error getting payment:', error);
    return null;
  }
}

export async function getPaymentsByOrderId(orderId: string): Promise<AdminPayment[]> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { data, error } = await supabase
      .from('payments' as any)
      .select('*')
      .eq('order_id', orderId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data as any as AdminPayment[];
  } catch (error) {
    console.error('Error getting payments for order:', error);
    return [];
  }
}

export async function updatePayment(id: string, params: UpdatePaymentParams): Promise<AdminPayment | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // First get the payment to check if status is changing to 'completed'
    const { data: existingPayment, error: fetchError } = await supabase
      .from('payments' as any)
      .select('*')
      .eq('id', id)
      .single();

    if (fetchError) throw fetchError;

    // Update the payment
    const { data, error } = await supabase
      .from('payments' as any)
      .update({
        ...params,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    // If payment status is being changed to 'completed', create a notification
    if (params.payment_status === 'completed' && (existingPayment as any).payment_status !== 'completed') {
      try {
        await NotificationService.createPaymentNotification(
          (data as any).order_id,
          (data as any).amount,
          (data as any).currency
        );
      } catch (notificationError) {
        console.error('Error creating payment notification:', notificationError);
        // Continue even if notification creation fails
      }
    }

    return data as any as AdminPayment;
  } catch (error) {
    console.error('Error updating payment:', error);
    return null;
  }
}

// Payout management
export async function getPayouts(params: PayoutListParams = {}): Promise<{ payouts: AdminPayout[], count: number }> {
  try {
    const {
      page = 1,
      per_page = 10,
      search = '',
      payout_status,
      store_id,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = params;

    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Build query
    let query = supabase.from('payouts' as any)
      .select('*, stores(name)', { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.or(`id.ilike.%${search}%,transaction_id.ilike.%${search}%`);
    }

    if (payout_status) {
      query = query.eq('payout_status', payout_status);
    }

    if (store_id) {
      query = query.eq('store_id', store_id);
    }

    // Apply pagination
    const from = (page - 1) * per_page;
    const to = from + per_page - 1;

    // Get results
    const { data, error, count } = await query
      .order(sort_by, { ascending: sort_order === 'asc' })
      .range(from, to);

    if (error) throw error;

    // Transform data to include store name
    const payouts = data.map((payout: any) => ({
      ...payout,
      store_name: payout.stores?.name,
      stores: undefined
    }));

    return {
      payouts: payouts as AdminPayout[],
      count: count || 0
    };
  } catch (error) {
    console.error('Error getting payouts:', error);
    return { payouts: [], count: 0 };
  }
}

export async function getPayout(id: string): Promise<AdminPayout | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { data, error } = await supabase
      .from('payouts' as any)
      .select('*, stores(name)')
      .eq('id', id)
      .single();

    if (error) throw error;

    return {
      ...(data as any),
      store_name: data.stores?.name,
      stores: undefined
    } as AdminPayout;
  } catch (error) {
    console.error('Error getting payout:', error);
    return null;
  }
}

export async function updatePayout(id: string, params: UpdatePayoutParams): Promise<AdminPayout | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { data, error } = await supabase
      .from('payouts' as any)
      .update({
        ...params,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data as any as AdminPayout;
  } catch (error) {
    console.error('Error updating payout:', error);
    return null;
  }
}

// Mark payout as sent and generate receipt
export async function confirmPayoutSent(payoutId: string, transactionId?: string): Promise<{ success: boolean, receipt?: any }> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Get payout details with related data
    const { data: payout, error: payoutError } = await supabase
      .from('payouts' as any)
      .select(`
        *,
        stores (name, contact_email),
        orders (id, total, currency, created_at),
        order_items!inner (product_id, quantity, price, total, products(name))
      `)
      .eq('id', payoutId)
      .single();

    if (payoutError || !payout) {
      throw new Error('Payout not found');
    }

    // Update payout status to sent
    const { error: updateError } = await supabase
      .from('payouts' as any)
      .update({
        payout_status: 'sent',
        transaction_id: transactionId,
        updated_at: new Date().toISOString()
      })
      .eq('id', payoutId);

    if (updateError) throw updateError;

    // Generate receipt data
    const receipt = {
      id: `RECEIPT-${payoutId.slice(-8).toUpperCase()}`,
      payout_id: payoutId,
      store_name: (payout as any).stores?.name,
      store_email: (payout as any).stores?.contact_email,
      order_id: (payout as any).order_id,
      amount: (payout as any).amount,
      commission: (payout as any).commission,
      currency: (payout as any).currency,
      transaction_id: transactionId,
      generated_at: new Date().toISOString(),
      items: (payout as any).order_items?.map((item: any) => ({
        product_name: item.products?.name,
        quantity: item.quantity,
        price: item.price,
        total: item.total
      })) || []
    };

    return { success: true, receipt };
  } catch (error) {
    console.error('Error confirming payout sent:', error);
    return { success: false };
  }
}

// Get store orders for store owners
export async function getStoreOrders(storeId: string): Promise<{ orders: any[], count: number }> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    console.log('getStoreOrders: Fetching orders for store:', storeId);

    // Get orders that have items from this store
    const { data: orderItems, error: itemsError } = await supabase
      .from('order_items' as any)
      .select('order_id')
      .eq('store_id', storeId);

    if (itemsError) {
      console.error('Error fetching order items:', itemsError);
      throw itemsError;
    }

    console.log('getStoreOrders: Found order items:', orderItems?.length || 0);

    if (!orderItems || orderItems.length === 0) {
      return { orders: [], count: 0 };
    }

    // Get unique order IDs
    const orderIds = [...new Set(orderItems.map(item => item.order_id))];

    // Get the actual orders - Explicitly select fields

    const { data: orders, error: ordersError, count } = await supabase
      .from('orders' as any)
      .select(
        `
        id,
        user_id,
        status,
        total,
        currency,
        created_at,
        updated_at,
        shipping_name,
        shipping_email,
        shipping_phone,
        shipping_address,
        shipping_city,
        shipping_state,
        shipping_country,
        shipping_postal_code,
        notes
        `,
        { count: 'exact' }
      )
      .in('id', orderIds)
      .order('created_at', { ascending: false });

    console.log('getStoreOrders: Orders query result:', { orders, error: ordersError, count });

    if (ordersError) {
      console.error('Error fetching orders:', ordersError);
      throw ordersError;
    }

    console.log('getStoreOrders: Found orders:', orders?.length || 0);
    console.log('getStoreOrders: Raw orders data:', orders);

    // Transform the data to match the expected format (similar to AdminOrder but can be any)
    const transformedOrders = orders?.map(order => {
      console.log('getStoreOrders: Transforming order:', (order as any).id, 'total:', (order as any).total, 'status:', (order as any).status);
      return {
        order_id: (order as any).id,
        store_id: storeId, // Add store_id here
        total_amount: (order as any).total || 0,
        currency: (order as any).currency || 'GMD',
        status: (order as any).status,
        created_at: (order as any).created_at,
        updated_at: (order as any).updated_at,
        order: { // Nested order object if needed by frontend
          id: (order as any).id,
          user_id: (order as any).user_id,
          status: (order as any).status,
          total: (order as any).total,
          currency: (order as any).currency,
          shipping_name: (order as any).shipping_name,
          shipping_email: (order as any).shipping_email,
          shipping_phone: (order as any).shipping_phone,
          created_at: (order as any).created_at,
          updated_at: (order as any).updated_at
        }
      };
    }) || [];

    console.log('getStoreOrders: Final transformed orders:', transformedOrders.length, transformedOrders);

    return {
      orders: transformedOrders,
      count: count || 0
    };
  } catch (error) {
    console.error('Error getting store orders:', error);
    return { orders: [], count: 0 };
  }
}

// Note: getStoreOwnerOrders function removed - store owners now use the unified getOrders function
// which automatically filters orders based on user role

// Get orders for multiple stores or all stores for a user
export async function getMultipleStoreOrders(storeIds?: string[]): Promise<{ orders: any[], count: number }> {
  try {
    console.log('getMultipleStoreOrders: Starting with storeIds:', storeIds);
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // If specific store IDs are provided, filter by them
    if (storeIds && storeIds.length > 0) {
      console.log('getMultipleStoreOrders: Fetching orders for specific stores:', storeIds);
      const allOrders = [];
      for (const storeId of storeIds) {
        console.log('getMultipleStoreOrders: Fetching orders for store:', storeId);
        const { orders } = await getStoreOrders(storeId);
        console.log('getMultipleStoreOrders: Got', orders.length, 'orders for store:', storeId);
        // Add store info to each order
        const ordersWithStore = orders.map(order => ({
          ...order,
          store_name: storeId // We'll get the actual store name separately if needed
        }));
        allOrders.push(...ordersWithStore);
      }

      console.log('getMultipleStoreOrders: Total orders from specific stores:', allOrders.length);

      // Sort by created_at descending
      allOrders.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      return {
        orders: allOrders,
        count: allOrders.length
      };
    } else {
      console.log('getMultipleStoreOrders: Fetching orders for all user stores');
      // Get all stores for the current user and fetch their orders
      const { stores, error: storesError } = await getCurrentUserStores();
      console.log('getMultipleStoreOrders: getCurrentUserStores result:', { stores: stores?.length, error: storesError });

      if (storesError || stores.length === 0) {
        console.log('getMultipleStoreOrders: No stores found or error:', storesError);
        return { orders: [], count: 0 };
      }

      const allOrders = [];
      for (const store of stores) {
        console.log('getMultipleStoreOrders: Fetching orders for store:', (store as any).id, (store as any).name);
        const result = await getStoreOrders((store as any).id);
        console.log('getMultipleStoreOrders: getStoreOrders result for', (store as any).name, ':', result);
        const orders = result.orders || [];
        console.log('getMultipleStoreOrders: Got', orders.length, 'orders for store:', (store as any).name);
        // Add store info to each order
        const ordersWithStore = orders.map(order => ({
          ...order,
          store_name: (store as any).name,
          store_id: (store as any).id
        }));
        console.log('getMultipleStoreOrders: Orders with store info:', ordersWithStore.length);
        allOrders.push(...ordersWithStore);
      }

      console.log('getMultipleStoreOrders: Total orders from all stores:', allOrders.length);

      // Sort by created_at descending
      allOrders.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      return {
        orders: allOrders,
        count: allOrders.length
      };
    }
  } catch (error) {
    console.error('Error getting multiple store orders:', error);
    return { orders: [], count: 0 };
  }
}

// Get store order details
export async function getStoreOrderDetails(orderId: string, storeId: string): Promise<any | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    console.log('getStoreOrderDetails: Fetching order details for order:', orderId, 'store:', storeId);

    // Get the order - Explicitly select fields
    const { data: order, error: orderError } = await supabase
      .from('orders' as any)
      .select(
        `
        id,
        user_id,
        status,
        total,
        currency,
        created_at,
        updated_at,
        shipping_name,
        shipping_email,
        shipping_phone,
        shipping_address,
        shipping_city,
        shipping_state,
        shipping_country,
        shipping_postal_code,
        notes
        `
      )
      .eq('id', orderId)
      .single();

    if (orderError) {
      console.error('Error fetching order:', orderError);
      throw orderError;
    }

    if (!order) {
      return null;
    }

    // Get order items for this store with product and store details
    const { data: items, error: itemsError } = await supabase
      .from('order_items' as any)
      .select(
        `
        id,
        product_id,
        store_id,
        quantity,
        price,
        total,
        products (
          name,
          product_images (
            url
          )
        ),
        stores (
          name
        )
        `
      )
      .eq('order_id', orderId)
      .eq('store_id', storeId);

    if (itemsError) {
      console.error('Error fetching order items:', itemsError);
      throw itemsError;
    }

    console.log('getStoreOrderDetails: Found items for store:', items?.length || 0);

    // Check if this store has any items in this order
    if (!items || items.length === 0) {
      console.log('getStoreOrderDetails: No items found for this store in this order');
      return null;
    }

    // Transform items data to match expected structure
    const transformedItems = items.map(item => ({
      id: item.id,
      product_id: item.product_id,
      store_id: item.store_id,
      quantity: item.quantity,
      price: item.price,
      total: item.total,
      product: {
        name: item.products?.name || 'Unknown Product',
        images: item.products?.product_images?.map((img: any) => ({ url: img.url })) || []
      },
      store: {
        name: item.stores?.name || 'Unknown Store'
      }
    }));

    // Get user email from profiles
    const { data: profileData } = await supabase
      .from('profiles' as any)
      .select('email')
      .eq('id', (order as any).user_id)
      .single();

    return {
      ...(order as any),
      user_email: profileData?.email,
      items: transformedItems,
      items_count: transformedItems.length,
      store_status: (order as any).status
    };
  } catch (error) {
    console.error('Error getting store order details:', error);
    return null;
  }
}

// Deal management
export async function getDeals(params: DealListParams = {}): Promise<{ deals: AdminDeal[], count: number }> {
  try {
    const {
      page = 1,
      per_page = 10,
      search = '',
      deal_type,
      is_active,
      featured,
      product_id,
      store_id,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = params;

    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Build query with product info only
    let query = supabase.from('deals' as any)
      .select(`
        *,
        products!inner(name, slug, store_id)
      `, { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`);
    }

    if (deal_type) {
      query = query.eq('deal_type', deal_type);
    }

    if (is_active !== undefined) {
      query = query.eq('is_active', is_active);
    }

    if (featured !== undefined) {
      query = query.eq('featured', featured);
    }

    if (product_id) {
      query = query.eq('product_id', product_id);
    }

    if (store_id) {
      query = query.eq('products.store_id', store_id);
    }

    // Apply pagination
    const from = (page - 1) * per_page;
    const to = from + per_page - 1;

    // Get results
    const { data, error, count } = await query
      .order(sort_by, { ascending: sort_order === 'asc' })
      .range(from, to);

    if (error) throw error;

    // Get store names for the deals
    const storeIds = [...new Set(data?.map((deal: any) => deal.products?.store_id).filter(Boolean))];
    let storeNames: Record<string, string> = {};

    if (storeIds.length > 0) {
      const { data: stores } = await supabase
        .from('stores')
        .select('id, name')
        .in('id', storeIds);

      storeNames = stores?.reduce((acc: Record<string, string>, store: any) => {
        acc[store.id] = store.name;
        return acc;
      }, {}) || {};
    }

    // Transform data to include product and store names
    const deals = data?.map((deal: any) => ({
      ...deal,
      product_name: deal.products?.name,
      product_slug: deal.products?.slug,
      store_id: deal.products?.store_id,
      store_name: storeNames[deal.products?.store_id] || null,
      products: undefined // Remove nested object
    })) || [];

    return {
      deals: deals as AdminDeal[],
      count: count || 0
    };
  } catch (error) {
    console.error('Error getting deals:', error);
    return { deals: [], count: 0 };
  }
}

export async function getDeal(id: string): Promise<AdminDeal | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { data, error } = await supabase
      .from('deals' as any)
      .select(`
        *,
        products!inner(name, slug, store_id)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;

    // Get store name
    let storeName = null;
    if (data.products?.store_id) {
      const { data: store } = await supabase
        .from('stores')
        .select('name')
        .eq('id', data.products.store_id)
        .single();
      storeName = store?.name;
    }

    return {
      ...data,
      product_name: data.products?.name,
      product_slug: data.products?.slug,
      store_id: data.products?.store_id,
      store_name: storeName,
      products: undefined
    } as AdminDeal;
  } catch (error) {
    console.error('Error getting deal:', error);
    return null;
  }
}

export async function createDeal(params: CreateDealParams): Promise<AdminDeal | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { data, error } = await supabase
      .from('deals' as any)
      .insert({
        ...params,
        currency: params.currency || 'GMD',
        is_active: params.is_active !== undefined ? params.is_active : true,
        featured: params.featured || false,
        used_quantity: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select(`
        *,
        products!inner(name, slug, store_id)
      `)
      .single();

    if (error) throw error;

    // Get store name
    let storeName = null;
    if (data.products?.store_id) {
      const { data: store } = await supabase
        .from('stores')
        .select('name')
        .eq('id', data.products.store_id)
        .single();
      storeName = store?.name;
    }

    return {
      ...data,
      product_name: data.products?.name,
      product_slug: data.products?.slug,
      store_id: data.products?.store_id,
      store_name: storeName,
      products: undefined
    } as AdminDeal;
  } catch (error) {
    console.error('Error creating deal:', error);
    return null;
  }
}

export async function updateDeal(id: string, params: UpdateDealParams): Promise<AdminDeal | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { data, error } = await supabase
      .from('deals' as any)
      .update({
        ...params,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        *,
        products!inner(name, slug, store_id)
      `)
      .single();

    if (error) throw error;

    // Get store name
    let storeName = null;
    if (data.products?.store_id) {
      const { data: store } = await supabase
        .from('stores')
        .select('name')
        .eq('id', data.products.store_id)
        .single();
      storeName = store?.name;
    }

    return {
      ...data,
      product_name: data.products?.name,
      product_slug: data.products?.slug,
      store_id: data.products?.store_id,
      store_name: storeName,
      products: undefined
    } as AdminDeal;
  } catch (error) {
    console.error('Error updating deal:', error);
    return null;
  }
}

export async function deleteDeal(id: string): Promise<boolean> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    
    // First get the deal to check if it exists
    const { data: deal, error: fetchError } = await supabase
      .from('deals' as any)
      .select('id, product_id')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error('Error fetching deal for deletion:', fetchError);
      return false;
    }

    if (!deal) {
      console.error('Deal not found for deletion');
      return false;
    }

    // Delete the deal
    const { error: deleteError } = await supabase
      .from('deals' as any)
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Error deleting deal:', deleteError);
      return false;
    }

    // Optional: Reset the product's compare_at_price to prevent fallback deals
    // This ensures the product won't show as a deal in the fallback method
    const { error: updateError } = await supabase
      .from('products' as any)
      .update({ compare_at_price: null })
      .eq('id', deal.product_id);

    if (updateError) {
      console.warn('Warning: Could not reset product compare_at_price:', updateError);
      // Don't fail the deletion if this update fails
    }

    console.log('Deal deleted successfully:', id);
    return true;
  } catch (error) {
    console.error('Error deleting deal:', error);
    return false;
  }
}

// Hero Slider management
export async function getSlides(params: SlideListParams = {}): Promise<{ slides: HeroSlide[], count: number }> {
  try {
    const {
      page = 1,
      per_page = 10,
      search = '',
      is_active,
      sort_by = 'position',
      sort_order = 'asc'
    } = params;

    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Build query
    let query = supabase.from('hero_slides' as any)
      .select('*', { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.or(`title.ilike.%${search}%,subtitle.ilike.%${search}%`);
    }

    if (is_active !== undefined) {
      query = query.eq('is_active', is_active);
    }

    // Apply pagination
    const from = (page - 1) * per_page;
    const to = from + per_page - 1;

    // Get results
    const { data, error, count } = await query
      .order(sort_by, { ascending: sort_order === 'asc' })
      .range(from, to);

    if (error) throw error;

    return {
      slides: data as HeroSlide[],
      count: count || 0
    };
  } catch (error) {
    console.error('Error getting slides:', error);
    return { slides: [], count: 0 };
  }
}

export async function getSlide(id: string): Promise<HeroSlide | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { data, error } = await supabase
      .from('hero_slides' as any)
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data as HeroSlide;
  } catch (error) {
    console.error('Error getting slide:', error);
    return null;
  }
}

export async function createSlide(params: CreateSlideParams): Promise<HeroSlide | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { data, error } = await supabase
      .from('hero_slides' as any)
      .insert({
        ...params,
        is_active: params.is_active !== undefined ? params.is_active : true,
        background_color: params.background_color || '#F78100',
        text_color: params.text_color || '#FFFFFF',
        link_text: params.link_text || 'Learn More',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) throw error;
    return data as HeroSlide;
  } catch (error) {
    console.error('Error creating slide:', error);
    return null;
  }
}

export async function updateSlide(id: string, params: UpdateSlideParams): Promise<HeroSlide | null> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { data, error } = await supabase
      .from('hero_slides' as any)
      .update({
        ...params,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data as HeroSlide;
  } catch (error) {
    console.error('Error updating slide:', error);
    return null;
  }
}

export async function deleteSlide(id: string): Promise<boolean> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { error } = await supabase
      .from('hero_slides' as any)
      .delete()
      .eq('id', id);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error deleting slide:', error);
    return false;
  }
}

// Get active slides for public display
export async function getActiveSlides(): Promise<HeroSlide[]> {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();
    const { data, error } = await supabase
      .from('hero_slides' as any)
      .select('*')
      .eq('is_active', true)
      .order('position', { ascending: true });

    if (error) throw error;
    return data as HeroSlide[];
  } catch (error) {
    console.error('Error getting active slides:', error);
    return [];
  }
}