import { createServerClient } from '@/lib/supabase/server';
import {
  Category,
  CategoryFilterParams
} from './types';

// Server-side API functions
export async function getCategories(filters: CategoryFilterParams = {}): Promise<Category[]> {
  try {
    const supabase = await createServerClient();
    let query = supabase.from('categories').select('*');

    if (filters.featured !== undefined) {
      query = query.eq('featured', filters.featured);
    }

    if (filters.parentId !== undefined) {
      if (filters.parentId === null) {
        query = query.is('parent_id', null);
      } else {
        query = query.eq('parent_id', filters.parentId);
      }
    }

    const { data, error } = await query.order('name');

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

export async function getCategoryBySlug(slug: string): Promise<Category | null> {
  try {
    const supabase = await createServerClient();
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('slug', slug)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error(`Error fetching category with slug ${slug}:`, error);
    return null;
  }
}

export async function getFeaturedCategories(limit = 6): Promise<Category[]> {
  try {
    const supabase = await createServerClient();
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('featured', true)
      .order('name')
      .limit(limit);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching featured categories:', error);
    return [];
  }
}
