# Product Shipping System

This document explains the new shipping classification system for products, which allows products to be tagged as either "local" or "international" with corresponding delivery and courier services.

## Overview

- **Local Products**: Use delivery services for local/regional shipping
- **International Products**: Use courier services for international shipping

## Database Schema

You'll need to create the following tables in your Supabase database:

### 1. delivery_services table
```sql
CREATE TABLE delivery_services (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  estimated_delivery_time VARCHAR(100) NOT NULL,
  cost DECIMAL(10,2) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. courier_services table
```sql
CREATE TABLE courier_services (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  description TEXT,
  estimated_delivery_time VARCHAR(100) NOT NULL,
  cost DECIMAL(10,2) NOT NULL,
  tracking_supported BOOLEAN DEFAULT false,
  countries TEXT[] DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3. product_shipping table
```sql
CREATE TABLE product_shipping (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  shipping_type VARCHAR(20) NOT NULL CHECK (shipping_type IN ('local', 'international')),
  delivery_service_id UUID REFERENCES delivery_services(id),
  courier_service_id UUID REFERENCES courier_services(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure only one shipping configuration per product
  UNIQUE(product_id),
  
  -- Ensure proper service selection based on shipping type
  CONSTRAINT shipping_service_check CHECK (
    (shipping_type = 'local' AND delivery_service_id IS NOT NULL AND courier_service_id IS NULL) OR
    (shipping_type = 'international' AND courier_service_id IS NOT NULL AND delivery_service_id IS NULL)
  )
);
```

### 4. Insert sample data

```sql
-- Sample delivery services
INSERT INTO delivery_services (name, description, estimated_delivery_time, cost) VALUES
('Same Day Delivery', 'Get your order delivered within 24 hours', '1-2 hours', 15.00),
('Standard Delivery', 'Regular delivery service', '2-3 business days', 5.00),
('Express Delivery', 'Fast delivery service', '1 business day', 10.00);

-- Sample courier services
INSERT INTO courier_services (name, description, estimated_delivery_time, cost, tracking_supported, countries) VALUES
('DHL Express', 'International express delivery', '3-5 business days', 25.00, true, ARRAY['US', 'CA', 'GB', 'DE', 'FR', 'AU']),
('FedEx International', 'Reliable international shipping', '5-7 business days', 20.00, true, ARRAY['US', 'CA', 'MX', 'GB', 'DE']),
('UPS Worldwide', 'Global shipping solution', '7-10 business days', 18.00, true, ARRAY['US', 'CA', 'GB', 'DE', 'FR', 'IT', 'ES']);
```

## Usage

### 1. Import the necessary components and hooks

```typescript
import { 
  ShippingSelector, 
  ShippingInfo, 
  useDeliveryServices, 
  useCourierServices,
  useProducts 
} from '@/features/products';
import type { ProductShipping } from '@/features/products';
```

### 2. Use ShippingSelector in product forms

```typescript
const [shipping, setShipping] = useState<ProductShipping>({
  shippingType: 'local'
});

<ShippingSelector
  currentShipping={shipping}
  onShippingChange={setShipping}
/>
```

### 3. Display shipping info on product cards

```typescript
<ShippingInfo 
  shipping={product.shipping} 
  compact // for product cards
/>

<ShippingInfo 
  shipping={product.shipping} 
  // full details for product pages
/>
```

### 4. Filter products by shipping

```typescript
const { data: products } = useProducts(
  { page: 1, limit: 10 },
  { 
    shippingType: 'local', // or 'international'
    deliveryServiceId: 'service-id', // optional
    courierServiceId: 'courier-id' // optional
  }
);
```

### 5. Get available services

```typescript
const { data: deliveryServices } = useDeliveryServices();
const { data: courierServices } = useCourierServices();
```

## Components

### ShippingSelector
Interactive component for selecting shipping type and services.

**Props:**
- `currentShipping?: ProductShipping` - Current shipping configuration
- `onShippingChange: (shipping: ProductShipping) => void` - Callback when shipping changes
- `className?: string` - Additional CSS classes

### ShippingInfo
Display component for showing shipping information.

**Props:**
- `shipping?: ProductShipping` - Shipping configuration to display
- `className?: string` - Additional CSS classes
- `compact?: boolean` - Whether to show compact version

## Query Hooks

### useDeliveryServices()
Fetches all active delivery services.

### useCourierServices()
Fetches all active courier services.

### useProducts() - Updated
Now supports additional filter parameters:
- `shippingType?: 'local' | 'international'`
- `deliveryServiceId?: string`
- `courierServiceId?: string`

## Types

### ShippingType
```typescript
type ShippingType = 'local' | 'international';
```

### DeliveryService
```typescript
interface DeliveryService {
  id: string;
  name: string;
  description?: string;
  estimatedDeliveryTime: string;
  cost: number;
  isActive: boolean;
}
```

### CourierService
```typescript
interface CourierService {
  id: string;
  name: string;
  description?: string;
  estimatedDeliveryTime: string;
  cost: number;
  trackingSupported: boolean;
  countries: string[];
  isActive: boolean;
}
```

### ProductShipping
```typescript
interface ProductShipping {
  shippingType: ShippingType;
  deliveryServiceId?: string;
  courierServiceId?: string;
  deliveryService?: DeliveryService;
  courierService?: CourierService;
}
```

## Example Implementation

See `ProductShippingExample.tsx` for a complete example of how to implement the shipping system in your application.

## Benefits

1. **Clear Separation**: Local vs international shipping with appropriate service types
2. **Flexible**: Easy to add new delivery or courier services
3. **Filterable**: Products can be filtered by shipping type and services
4. **User-Friendly**: Clear UI components for selection and display
5. **Extensible**: Easy to add more shipping-related features

## Next Steps

1. Create the database tables
2. Add sample data
3. Integrate the components into your product forms and displays
4. Test the filtering functionality
5. Customize the styling to match your application