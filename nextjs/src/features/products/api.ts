import { createSPASassClient } from '@/lib/supabase/client';
import { createServerClient } from '@/lib/supabase/server';
import {
  Product,
  PaginationParams,
  ProductFilterParams,
  PaginatedResponse,
  ProductsApiResponse,
  ProductApiResponse
} from './types';

// Server-side API functions
export async function getProducts(
  paginationParams: PaginationParams = { page: 1, limit: 10 },
  filterParams: ProductFilterParams = {}
): Promise<PaginatedResponse<Product>> {
  try {
    const supabase = await createServerClient();

    let query = supabase
      .from('products')
      .select(`
        *,
        category:categories(id, name, slug),
        vendor:vendors(id, name)
      `);

    // Apply filters
    if (filterParams.categoryId) {
      query = query.eq('category_id', filterParams.categoryId);
    }

    if (filterParams.vendorId) {
      query = query.eq('vendor_id', filterParams.vendorId);
    }

    if (filterParams.featured !== undefined) {
      query = query.eq('featured', filterParams.featured);
    }

    if (filterParams.inStock !== undefined) {
      query = query.eq('in_stock', filterParams.inStock);
    }

    if (filterParams.minPrice !== undefined) {
      query = query.gte('price', filterParams.minPrice);
    }

    if (filterParams.maxPrice !== undefined) {
      query = query.lte('price', filterParams.maxPrice);
    }

    if (filterParams.search) {
      query = query.or(`name.ilike.%${filterParams.search}%,description.ilike.%${filterParams.search}%`);
    }

    // Apply pagination
    const from = (paginationParams.page - 1) * paginationParams.limit;
    const to = from + paginationParams.limit - 1;

    const { data, error, count } = await query
      .range(from, to)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    return {
      data: data || [],
      total: count || 0,
      page: paginationParams.page,
      limit: paginationParams.limit,
      totalPages: Math.ceil((count || 0) / paginationParams.limit)
    };

    // if (error) throw error;
    // return { data: data || [], meta: { ... } };
  } catch (error) {
    console.error('Error fetching products:', error);
    return {
      data: [],
      total: 0,
      page: paginationParams.page,
      limit: paginationParams.limit,
      totalPages: 0
    };
  }
}

export async function getProductBySlug(slug: string): Promise<Product | null> {
  try {
    const supabase = await createServerClient();
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        category:categories(id, name, slug),
        vendor:vendors(id, name)
      `)
      .eq('slug', slug)
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error(`Error fetching product with slug ${slug}:`, error);
    return null;
  }
}

export async function getFeaturedProducts(limit = 8): Promise<Product[]> {
  try {
    const supabase = await createServerClient();
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        category:categories(id, name, slug),
        vendor:vendors(id, name)
      `)
      .eq('featured', true)
      .limit(limit)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching featured products:', error);
    return [];
  }
}

export async function getRelatedProducts(productId: string, categoryId: string, limit: number = 4): Promise<Product[]> {
  try {
    const supabase = await createServerClient();
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        category:categories(id, name, slug),
        vendor:vendors(id, name)
      `)
      .eq('category_id', categoryId)
      .neq('id', productId)
      .limit(limit)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching related products:', error);
    return [];
  }
}

// Client-side API functions
export async function getProductsClient(
  paginationParams: PaginationParams = { page: 1, limit: 10 },
  filterParams: ProductFilterParams = {}
): Promise<ProductsApiResponse> {
  try {
    const response = await fetch(`/api/products?${new URLSearchParams({
      page: paginationParams.page.toString(),
      limit: paginationParams.limit.toString(),
      ...(filterParams.categoryId && { categoryId: filterParams.categoryId }),
      ...(filterParams.vendorId && { vendorId: filterParams.vendorId }),
      ...(filterParams.featured !== undefined && { featured: filterParams.featured.toString() }),
      ...(filterParams.inStock !== undefined && { inStock: filterParams.inStock.toString() }),
      ...(filterParams.minPrice !== undefined && { minPrice: filterParams.minPrice.toString() }),
      ...(filterParams.maxPrice !== undefined && { maxPrice: filterParams.maxPrice.toString() }),
      ...(filterParams.search && { search: filterParams.search })
    })}`);

    if (!response.ok) {
      throw new Error('Failed to fetch products');
    }

    const data = await response.json();
    return { products: data };
  } catch (error) {
    console.error('Error fetching products:', error);
    return {
      products: {
        data: [],
        total: 0,
        page: paginationParams.page,
        limit: paginationParams.limit,
        totalPages: 0
      },
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}
