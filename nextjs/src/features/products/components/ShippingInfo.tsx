'use client';

import { ProductShipping } from '../types';
import { calculateShippingCost } from '../utils/shipping';

interface ShippingInfoProps {
  shipping?: ProductShipping;
  productWeight?: number; // Weight in kg for cost calculation
  className?: string;
  compact?: boolean;
}

export function ShippingInfo({ shipping, productWeight, className = '', compact = false }: ShippingInfoProps) {
  if (!shipping) {
    return null;
  }

  const { shippingType, deliveryService, courierService } = shipping;
  const isLocal = shippingType === 'local';
  const service = isLocal ? deliveryService : courierService;

  if (!service) {
    return (
      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
        isLocal 
          ? 'bg-blue-100 text-blue-800' 
          : 'bg-green-100 text-green-800'
      } ${className}`}>
        {isLocal ? '📍 Local' : '🌍 International'}
      </div>
    );
  }

  if (compact) {
    return (
      <div className={`inline-flex items-center space-x-1 ${className}`}>
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          isLocal 
            ? 'bg-blue-100 text-blue-800' 
            : 'bg-green-100 text-green-800'
        }`}>
          {isLocal ? '📍' : '🌍'} {service.name}
        </span>
        <span className="text-xs text-gray-500">
          ${service.cost}
        </span>
      </div>
    );
  }

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center justify-between">
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          isLocal 
            ? 'bg-blue-100 text-blue-800' 
            : 'bg-green-100 text-green-800'
        }`}>
          {isLocal ? '📍 Local Delivery' : '🌍 International Shipping'}
        </span>
        <span className="text-sm font-medium text-gray-900">
          ${service.cost}
        </span>
      </div>
      
      <div className="text-sm text-gray-600">
        <div className="flex items-center justify-between">
          <span>Service:</span>
          <span className="font-medium">{service.name}</span>
        </div>
        <div className="flex items-center justify-between">
          <span>Delivery time:</span>
          <span>{service.estimatedDeliveryTime}</span>
        </div>
        
        {!isLocal && service.trackingSupported && (
          <div className="flex items-center justify-between">
            <span>Tracking:</span>
            <span className="text-green-600">✓ Available</span>
          </div>
        )}
        
        {!isLocal && service.countries.length > 0 && (
          <div className="mt-1">
            <span className="text-xs text-gray-500">
              Ships to: {service.countries.slice(0, 3).join(', ')}
              {service.countries.length > 3 && ` +${service.countries.length - 3} more`}
            </span>
          </div>
        )}
      </div>
      
      {service.description && (
        <p className="text-xs text-gray-500 mt-1">
          {service.description}
        </p>
      )}
    </div>
  );
}