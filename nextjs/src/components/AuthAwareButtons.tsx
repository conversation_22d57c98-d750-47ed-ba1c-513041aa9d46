"use client";
import { useAuth } from '@/lib/hooks/useAuth';
import Link from 'next/link';
import { Button } from './ui/button';

export default function AuthAwareButtons() {
    const { user, role, loading, isAdmin } = useAuth();

    if (loading) {
        return null;
    }

    if (!user) {
        return (
            <div className="flex items-center gap-4">
                <Link href="/auth/login">
                    <Button variant="ghost">Sign in</Button>
                </Link>
                <Link href="/auth/register">
                    <Button>Sign up</Button>
                </Link>
            </div>
        );
    }

    return (
        <div className="flex items-center gap-4">
            {isAdmin && (
                <Link href="/admin">
                    <Button variant="ghost">Admin Dashboard</Button>
                </Link>
            )}
            <Link href="/account">
                <Button variant="ghost">My Account</Button>
            </Link>
        </div>
    );
}