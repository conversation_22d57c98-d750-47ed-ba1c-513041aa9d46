"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Search,
  ShoppingCart,
  Heart,
  User,
  Menu,
  X,
  ChevronDown,
  LogOut
} from 'lucide-react';
import Image from 'next/image';
import { createSPASassClient } from '@/lib/supabase/client';
import { Category } from '@/lib/types/ecommerce';
import { EcommerceClientService } from '@/lib/services/ecommerce-client';
import { useCart } from '@/lib/context/CartContext';
import { NotificationDropdown } from '@/components/notifications/NotificationDropdown';

// Types
interface User {
  email: string;
  id: string;
}

interface NavigationItem {
  name: string;
  href: string;
}

// Constants
const PRODUCT_NAME = process.env.NEXT_PUBLIC_PRODUCTNAME || "Finder";
const MAX_CATEGORIES_DISPLAY = 10;
const MOBILE_MENU_BREAKPOINT = 1024;

// Remove stores from navigation
const NAVIGATION: NavigationItem[] = [
  { name: 'Home', href: '/' },
  { name: 'Products', href: '/products' },
  { name: 'Categories', href: '/categories' },
  { name: 'Deals', href: '/deals' }
];

export default function Header() {
  // State management
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const [isCategoriesDropdownOpen, setIsCategoriesDropdownOpen] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [userRole, setUserRole] = useState<string>('user');
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [windowWidth, setWindowWidth] = useState<number>(0);

  // Refs for dropdown management
  const userDropdownRef = useRef<HTMLDivElement>(null);
  const categoriesDropdownRef = useRef<HTMLDivElement>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  const { cartCount } = useCart();
  const pathname = usePathname();

  // Track window width for responsive behavior
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
      // Close mobile menu when resizing to desktop
      if (window.innerWidth >= MOBILE_MENU_BREAKPOINT) {
        setIsMenuOpen(false);
      }
    };

    setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Utility functions
  const getInitials = useCallback((email: string): string => {
    const parts = email.split('@')[0].split(/[._-]/);
    return parts.length > 1
      ? (parts[0][0] + parts[1][0]).toUpperCase()
      : parts[0].slice(0, 2).toUpperCase();
  }, []);

  const closeAllDropdowns = useCallback(() => {
    setIsUserDropdownOpen(false);
    setIsCategoriesDropdownOpen(false);
  }, []);

  // Authentication functions
  const checkAuth = useCallback(async () => {
    try {
      const supabase = await createSPASassClient();
      const { data: { user } } = await supabase.getSupabaseClient().auth.getUser();

      setIsAuthenticated(!!user);

      if (user) {
        setUser({ email: user.email || '', id: user.id });

        // Fetch user role
        try {
          const { data: profile } = await supabase.getSupabaseClient()
            .from('profiles')
            .select('role')
            .eq('id', user.id)
            .single();

          setUserRole(profile?.role || 'user');
        } catch (roleError) {
          console.warn('Could not fetch user role:', roleError);
          setUserRole('user');
        }
      } else {
        setUser(null);
        setUserRole('user');
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      setIsAuthenticated(false);
      setUser(null);
      setUserRole('user');
    } finally {
      setLoading(false);
    }
  }, []);

  const handleLogout = useCallback(async () => {
    try {
      const client = await createSPASassClient();
      await client.logout();
      setIsAuthenticated(false);
      setUser(null);
      setUserRole('user');
      closeAllDropdowns();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  }, [closeAllDropdowns]);

  // Data fetching
  const fetchCategories = useCallback(async () => {
    try {
      const data = await EcommerceClientService.getCategories();
      setCategories(data);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  }, []);

  // Event handlers
  const handleMobileMenuToggle = useCallback(() => {
    setIsMenuOpen(prev => !prev);
    closeAllDropdowns();
  }, [closeAllDropdowns]);

  const handleDropdownClick = useCallback((dropdownType: 'user' | 'categories') => {
    if (dropdownType === 'user') {
      setIsUserDropdownOpen(prev => !prev);
      setIsCategoriesDropdownOpen(false);
    } else {
      setIsCategoriesDropdownOpen(prev => !prev);
      setIsUserDropdownOpen(false);
    }
  }, []);

  // Click outside handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const isMobile = windowWidth < MOBILE_MENU_BREAKPOINT;

      if (
        userDropdownRef.current &&
        !userDropdownRef.current.contains(event.target as Node) &&
        categoriesDropdownRef.current &&
        !categoriesDropdownRef.current.contains(event.target as Node) &&
        (!isMobile || (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node)))
      ) {
        closeAllDropdowns();
        if (isMobile) {
          setIsMenuOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [closeAllDropdowns, windowWidth]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        closeAllDropdowns();
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [closeAllDropdowns]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMenuOpen && windowWidth < MOBILE_MENU_BREAKPOINT) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isMenuOpen, windowWidth]);

  // Initial data loading
  useEffect(() => {
    checkAuth();
    fetchCategories();
  }, [checkAuth, fetchCategories]);

  // Render functions
  const renderLogo = () => (
    <div className="flex flex-shrink-0 items-center">
      <Link href="/" className="block" aria-label="Home">
        <div className="relative h-[7rem] w-[7rem] md:h-[8rem] md:w-[8rem] lg:h-[9rem] lg:w-[9rem] overflow-hidden">
          <Image
            src="/lolo.png"
            alt={`${PRODUCT_NAME} logo`}
            fill
            sizes="(max-width: 768px) 64px, (max-width: 1024px) 80px, 96px"
            className="object-contain"
            priority
          />
        </div>
      </Link>
    </div>
  );

  const renderDesktopNavigation = () => (
    <nav className="hidden lg:flex lg:space-x-4 xl:space-x-6" role="navigation" aria-label="Main navigation">
      {NAVIGATION.map((item) => (
        <Link
          key={item.name}
          href={item.href}
          className={`inline-flex items-center px-1 py-2 text-sm font-medium transition-colors ${pathname === item.href
              ? 'text-primary-600 border-b-2 border-primary-600'
              : 'text-gray-500 hover:text-gray-900'
            }`}
          aria-current={pathname === item.href ? 'page' : undefined}
        >
          {item.name}
        </Link>
      ))}

      {/* Categories dropdown */}
      <div className="relative" ref={categoriesDropdownRef}>
        <button
          type="button"
          className="inline-flex items-center px-1 py-2 text-sm font-medium text-gray-500 hover:text-gray-900 transition-colors"
          onClick={() => handleDropdownClick('categories')}
          aria-expanded={isCategoriesDropdownOpen}
          aria-haspopup="true"
        >
          Categories
          <ChevronDown
            className={`ml-1 h-4 w-4 transition-transform ${isCategoriesDropdownOpen ? 'rotate-180' : ''
              }`}
          />
        </button>

        {isCategoriesDropdownOpen && (
          <div className="absolute left-0 z-50 mt-2 w-64 origin-top-left rounded-md bg-white py-2 shadow-lg ring-1 ring-black ring-opacity-5 max-h-[70vh] overflow-y-auto">
            <div className="grid grid-cols-2 gap-1 px-2">
              {categories.slice(0, MAX_CATEGORIES_DISPLAY).map((category) => (
                <Link
                  key={category.id}
                  href={`/categories/${category.slug}`}
                  className="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md truncate transition-colors"
                  onClick={closeAllDropdowns}
                  title={category.name}
                >
                  {category.name}
                </Link>
              ))}
            </div>
            {categories.length > MAX_CATEGORIES_DISPLAY && (
              <div className="border-t border-gray-100 mt-2 pt-2">
                <Link
                  href="/categories"
                  className="block px-4 py-2 text-sm text-primary-600 hover:bg-primary-50 font-medium text-center transition-colors"
                  onClick={closeAllDropdowns}
                >
                  View All Categories ({categories.length})
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </nav>
  );

  const renderActionButtons = () => (
    <div className="flex items-center space-x-1 sm:space-x-2">
      {/* Search */}
      <Link
        href="/find"
        className="p-2 text-gray-400 hover:text-gray-500 transition-colors rounded-md hover:bg-gray-100"
        aria-label="Search products"
      >
        <Search className="h-5 w-5 sm:h-6 sm:w-6" />
      </Link>

      {/* Wishlist */}
      <Link
        href="/wishlist"
        className="p-2 text-gray-400 hover:text-gray-500 transition-colors rounded-md hover:bg-gray-100"
        aria-label="View wishlist"
      >
        <Heart className="h-5 w-5 sm:h-6 sm:w-6" />
      </Link>

      {/* Cart */}
      <Link
        href="/cart"
        className="relative p-2 text-gray-400 hover:text-gray-500 transition-colors rounded-md hover:bg-gray-100"
        aria-label={`Shopping cart with ${cartCount} items`}
      >
        <ShoppingCart className="h-5 w-5 sm:h-6 sm:w-6" />
        {cartCount > 0 && (
          <span
            className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full bg-primary-600 text-xs font-bold text-white"
            aria-label={`${cartCount} items in cart`}
          >
            {cartCount > 99 ? '99+' : cartCount}
          </span>
        )}
      </Link>

      {/* Notifications */}
      {!loading && isAuthenticated && <NotificationDropdown />}
    </div>
  );

  const renderUserSection = () => {
    if (loading) {
      return (
        <div className="h-8 w-8 rounded-full bg-gray-200 animate-pulse" />
      );
    }

    if (!isAuthenticated) {
      return (
        <Link
          href="/auth/login"
          className="flex items-center space-x-2 rounded-md bg-primary-600 px-3 py-1.5 sm:px-4 sm:py-2 text-sm font-medium text-white hover:bg-primary-700 transition-colors"
        >
          <User className="h-4 w-4" />
          <span className="hidden sm:inline">Sign In</span>
        </Link>
      );
    }

    return (
      <div className="relative" ref={userDropdownRef}>
        <button
          type="button"
          className="flex items-center space-x-1 rounded-full text-sm text-gray-700 hover:text-gray-900 transition-colors p-1 hover:bg-gray-100"
          onClick={() => handleDropdownClick('user')}
          aria-expanded={isUserDropdownOpen}
          aria-haspopup="true"
          aria-label="User menu"
        >
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 text-primary-700 font-medium">
            {user?.email ? getInitials(user.email) : '?'}
          </div>
        </button>

        {isUserDropdownOpen && (
          <div className="absolute right-0 z-50 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5">
            <div className="border-b border-gray-100 px-4 py-2">
              <p className="text-xs text-gray-500">Signed in as</p>
              <p className="truncate text-sm font-medium text-gray-900">
                {user?.email}
              </p>
            </div>

            <Link
              href="/account"
              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
              onClick={closeAllDropdowns}
            >
              My Account
            </Link>
            <Link
              href="/my-orders"
              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
              onClick={closeAllDropdowns}
            >
              My Orders
            </Link>
            <Link
              href="/notifications"
              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
              onClick={closeAllDropdowns}
            >
              Notifications
            </Link>

            {/* Admin access */}
            {(userRole === 'admin') && (
              <Link
                href="/admin"
                className="block px-4 py-2 text-sm text-blue-600 hover:bg-blue-50 font-medium transition-colors"
                onClick={closeAllDropdowns}
              >
                🛡️ Admin Panel
              </Link>
            )}

            <div className="border-t border-gray-100 mt-1 pt-1">
              <button
                type="button"
                className="flex items-center w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 transition-colors"
                onClick={handleLogout}
              >
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </button>
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderMobileMenu = () => (
    <div
      className={`fixed inset-0 z-40 lg:hidden ${isMenuOpen ? 'block' : 'hidden'}`}
      role="dialog"
      aria-modal="true"
      ref={mobileMenuRef}
    >
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black bg-opacity-25 transition-opacity"
        onClick={() => setIsMenuOpen(false)}
        aria-hidden="true"
      />

      {/* Menu panel */}
      <div className="fixed inset-y-0 left-0 z-50 w-full max-w-xs overflow-y-auto bg-white pb-12 shadow-xl transition-transform duration-300 ease-in-out transform translate-x-0">
        <div className="flex items-center justify-between px-4 pt-5">
          <Link href="/" className="text-lg font-semibold text-gray-900">
            {PRODUCT_NAME}
          </Link>
          <button
            type="button"
            className="inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 transition-colors"
            onClick={() => setIsMenuOpen(false)}
            aria-label="Close menu"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Navigation Links */}
        <div className="mt-6 space-y-1 px-4">
          {NAVIGATION.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className={`block rounded-md px-3 py-2 text-base font-medium transition-colors ${pathname === item.href
                  ? 'bg-primary-50 text-primary-600'
                  : 'text-gray-900 hover:bg-gray-100'
                }`}
              onClick={() => setIsMenuOpen(false)}
            >
              {item.name}
            </Link>
          ))}

          {/* Categories section */}
          {categories.length > 0 && (
            <div className="py-2">
              <h3 className="px-3 text-sm font-medium text-gray-500 uppercase tracking-wide">
                Categories
              </h3>
              <div className="mt-2 space-y-1">
                {categories.slice(0, 8).map((category) => (
                  <Link
                    key={category.id}
                    href={`/categories/${category.slug}`}
                    className="block rounded-md px-3 py-2 text-base text-gray-900 hover:bg-gray-100 transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {category.name}
                  </Link>
                ))}
                {categories.length > 8 && (
                  <Link
                    href="/categories"
                    className="block px-3 py-2 text-sm text-primary-600 font-medium"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    View All Categories
                  </Link>
                )}
              </div>
            </div>
          )}

          {/* User section */}
          {isAuthenticated ? (
            <div className="border-t border-gray-200 pt-4">
              <div className="flex items-center px-3 py-2">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary-100 text-primary-700 font-medium">
                  {user?.email ? getInitials(user.email) : '?'}
                </div>
                <span className="ml-3 truncate text-sm font-medium text-gray-900">
                  {user?.email}
                </span>
              </div>
              <div className="mt-2 space-y-1">
                <Link
                  href="/account"
                  className="block rounded-md px-3 py-2 text-base text-gray-900 hover:bg-gray-100 transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  My Account
                </Link>
                <Link
                  href="/my-orders"
                  className="block rounded-md px-3 py-2 text-base text-gray-900 hover:bg-gray-100 transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  My Orders
                </Link>
                <Link
                  href="/notifications"
                  className="block rounded-md px-3 py-2 text-base text-gray-900 hover:bg-gray-100 transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Notifications
                </Link>

                {(userRole === 'admin') && (
                  <Link
                    href="/admin"
                    className="block rounded-md px-3 py-2 text-base text-blue-600 hover:bg-blue-50 font-medium transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    🛡️ Admin Panel
                  </Link>
                )}

                <button
                  type="button"
                  className="flex items-center w-full rounded-md px-3 py-2 text-left text-base text-red-600 hover:bg-red-50 transition-colors"
                  onClick={() => {
                    handleLogout();
                    setIsMenuOpen(false);
                  }}
                >
                  <LogOut className="h-5 w-5 mr-2" />
                  Sign Out
                </button>
              </div>
            </div>
          ) : (
            <div className="border-t border-gray-200 pt-4 space-y-1">
              <Link
                href="/auth/login"
                className="block rounded-md px-3 py-2 text-base font-medium text-gray-900 hover:bg-gray-100 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Sign In
              </Link>
              <Link
                href="/auth/register"
                className="block rounded-md px-3 py-2 text-base font-medium text-gray-900 hover:bg-gray-100 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                Create Account
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <header className="relative bg-white shadow-sm">
      {/* Top promotional bar */}
      <div className="bg-primary-600 py-1.5 text-center text-xs sm:text-sm text-white">
        <p>Free delivery within The Gambia. Shop with confidence!</p>
      </div>

      <div className="mx-auto max-w-7xl px-2 sm:px-4 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Mobile menu button */}
          <div className="flex lg:hidden">
            <button
              type="button"
              className="inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 transition-colors"
              onClick={handleMobileMenuToggle}
              aria-expanded={isMenuOpen}
              aria-controls="mobile-menu"
              aria-label="Open main menu"
            >
              <Menu className="h-6 w-6" />
            </button>
          </div>

          {renderLogo()}
          {renderDesktopNavigation()}

          {/* Right section */}
          <div className="flex items-center space-x-2 sm:space-x-4">
            {renderActionButtons()}
            {renderUserSection()}
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {renderMobileMenu()}
    </header>
  );
}