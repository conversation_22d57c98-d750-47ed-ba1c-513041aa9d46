"use client";
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Users,
  Package,
  Settings,
  Menu,
  X,
  ChevronDown,
  BarChart3,
  ShoppingCart,
  FolderTree,
  Tag,
  Image,
  Wallet,
  Store,
  Bell
} from 'lucide-react';
import { useGlobal } from "@/lib/context/GlobalContext";
import { createSPASassClient } from "@/lib/supabase/client";
import { getUserInitials } from '@/lib/utils/user';
import { NotificationClientService } from '@/lib/services/notification-client';
import { useToast } from '@/lib/hooks/use-toast';
import { useAuth } from '@/lib/hooks/useAuth';
import { featureToggleService } from '@/lib/services/feature-toggles';
import { FeatureKey } from '@/lib/types/roles';

export default function AdminLayout({ children }: { children: React.ReactNode }) {
  const [isSidebarOpen, setSidebarOpen] = useState(false);
  const [isUserDropdownOpen, setUserDropdownOpen] = useState(false);
  const [isNotificationsOpen, setNotificationsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [enabledFeatures, setEnabledFeatures] = useState<Set<FeatureKey>>(new Set());
  const [featuresInitialized, setFeaturesInitialized] = useState(false);
  const pathname = usePathname();
  const { user } = useGlobal();
  const { role } = useAuth();
  const { toast } = useToast();

  // Helper function to get default features based on role
  const getDefaultFeatures = (userRole: string): Set<FeatureKey> => {
    const defaultFeatures = new Set<FeatureKey>();
    if (userRole === 'admin') {
      defaultFeatures.add('analytics_view');
      defaultFeatures.add('user_management');
      defaultFeatures.add('product_management');
      defaultFeatures.add('vendor_management');
      defaultFeatures.add('order_management');
      defaultFeatures.add('payment_management');
      defaultFeatures.add('payout_management');
      defaultFeatures.add('deal_management');
      defaultFeatures.add('category_management');
      defaultFeatures.add('slider_management');
    }
    return defaultFeatures;
  };

  // Initialize feature toggles
  useEffect(() => {
    const initializeFeatures = async () => {
      if (!role) {
        // If no role, set empty features and mark as initialized
        setEnabledFeatures(new Set());
        setFeaturesInitialized(true);
        return;
      }

      // Start with default features immediately to prevent flickering
      const defaultFeatures = getDefaultFeatures(role);
      setEnabledFeatures(defaultFeatures);
      setFeaturesInitialized(true);

      try {
        await featureToggleService.initialize();
        const features = new Set<FeatureKey>();

        // Check each feature for the current user role
        const featureKeys: FeatureKey[] = [
          'analytics_view',
          'user_management',
          'product_management',
          'vendor_management',
          'order_management',
          'payment_management',
          'payout_management',
          'deal_management',
          'category_management',
          'slider_management'
        ];

        featureKeys.forEach(key => {
          const isEnabled = featureToggleService.isFeatureEnabled(key, role);
          if (isEnabled) {
            features.add(key);
          }
        });

        // Only update if we have features, otherwise keep defaults
        if (features.size > 0) {
          setEnabledFeatures(features);
        }
      } catch (error) {
        // Keep the default features we already set
      }
    };

    initializeFeatures();
  }, [role]);

  // Set up real-time listener for notifications
  useEffect(() => {
    const setupNotificationListener = async () => {
      if (!user) return;

      try {
        const client = await createSPASassClient();
        const supabase = client.getSupabaseClient();

        // Subscribe to notifications for this user
        const channel = supabase
          .channel('admin-notifications')
          .on(
            'postgres_changes',
            {
              event: 'INSERT',
              schema: 'public',
              table: 'notifications',
              filter: `user_id=eq.${user.id}`
            },
            (payload) => {
              // Show toast notification
              if (payload.new && payload.new.type === 'payment') {
                toast({
                  title: payload.new.title,
                  description: payload.new.content,
                  variant: "success",
                });

                // Update unread count
                setUnreadCount(prev => prev + 1);

                // Update notifications list if open
                if (isNotificationsOpen) {
                  loadNotifications();
                }
              }
            }
          )
          .subscribe();

        // Cleanup subscription on unmount
        return () => {
          supabase.removeChannel(channel);
        };
      } catch (error) {
        console.error('Error setting up notification listener:', error);
      }
    };

    setupNotificationListener();

    // Load initial unread count
    loadUnreadCount();
  }, [user]);

  const loadUnreadCount = async () => {
    if (!user) return;

    try {
      const count = await NotificationClientService.getUnreadCount();
      setUnreadCount(count);
    } catch (error) {
      console.error('Error loading unread count:', error);
    }
  };

  const loadNotifications = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const { notifications } = await NotificationClientService.getUserNotifications(5);
      setNotifications(notifications);
    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationsClick = () => {
    const newState = !isNotificationsOpen;
    setNotificationsOpen(newState);

    if (newState) {
      loadNotifications();
    }
  };

  const markAllAsRead = async () => {
    try {
      await NotificationClientService.markAllAsRead();
      setUnreadCount(0);
      loadNotifications();
    } catch (error) {
      console.error('Error marking notifications as read:', error);
    }
  };

  const handleLogout = async () => {
    try {
      const client = await createSPASassClient();
      await client.logout();
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  // Define all possible navigation items with their feature requirements
  const allNavigationItems = [
    { name: 'Dashboard', href: '/admin', icon: BarChart3, feature: 'analytics_view' as FeatureKey },
    { name: 'Users', href: '/admin/users', icon: Users, feature: 'user_management' as FeatureKey },
    { name: 'Products', href: '/admin/products', icon: Package, feature: 'product_management' as FeatureKey },
    { name: 'Vendors', href: '/admin/vendors', icon: Store, feature: 'vendor_management' as FeatureKey },
    { name: 'Orders', href: '/admin/orders', icon: ShoppingCart, feature: 'order_management' as FeatureKey },
    { name: 'Categories', href: '/admin/categories', icon: FolderTree, feature: 'category_management' as FeatureKey },
    { name: 'Deals', href: '/admin/deals', icon: Tag, feature: 'deal_management' as FeatureKey },
    { name: 'Slider', href: '/admin/slider', icon: Image, feature: 'slider_management' as FeatureKey },
    { name: 'Payouts', href: '/admin/payouts', icon: Wallet, feature: 'payout_management' as FeatureKey },
    { name: 'Settings', href: '/admin/settings', icon: Settings, feature: 'settings_access' as FeatureKey },
  ];

  // Filter navigation based on enabled features
  const navigation = allNavigationItems.filter(item =>
    enabledFeatures.has(item.feature)
  );



  const toggleSidebar = () => setSidebarOpen(!isSidebarOpen);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isNotificationsOpen || isUserDropdownOpen) {
        const target = event.target as HTMLElement;
        if (!target.closest('.notifications-container') && !target.closest('.user-dropdown-container')) {
          setNotificationsOpen(false);
          setUserDropdownOpen(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isNotificationsOpen, isUserDropdownOpen]);

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Mobile sidebar backdrop */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-gray-600 bg-opacity-75 z-20 lg:hidden"
          onClick={toggleSidebar}
        />
      )}

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-30 w-64 bg-white shadow-lg transform ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } lg:translate-x-0 transition-transform duration-300 ease-in-out`}
      >
        {/* Sidebar header */}
        <div className="flex items-center justify-between h-16 px-4 border-b">
          <Link href="/admin" className="flex items-center">
            <span className="text-xl font-bold text-primary-600">Synergy Admin</span>
          </Link>
          <button
            onClick={toggleSidebar}
            className="lg:hidden text-gray-500 hover:text-gray-700"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Sidebar content */}
        <div className="h-full overflow-y-auto">
          {/* Navigation */}
          <nav className="mt-4 px-2 space-y-1">
            {!featuresInitialized ? (
              // Show loading skeleton while features are initializing
              <div className="space-y-1">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="flex items-center px-2 py-2">
                    <div className="w-5 h-5 bg-gray-200 rounded mr-3 animate-pulse"></div>
                    <div className="h-4 bg-gray-200 rounded animate-pulse flex-1"></div>
                  </div>
                ))}
              </div>
            ) : (
              navigation.map((item) => {
                const isActive = pathname === item.href ||
                  (item.href !== '/admin' && pathname?.startsWith(item.href));
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md ${
                      isActive
                        ? 'bg-primary-50 text-primary-600'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <item.icon
                      className={`mr-3 h-5 w-5 ${
                        isActive ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'
                      }`}
                    />
                    {item.name}
                  </Link>
                );
              })
            )}
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top navbar */}
        <div className="sticky top-0 z-40 flex items-center justify-between h-16 bg-white shadow-sm px-4">
          <button
            onClick={toggleSidebar}
            className="lg:hidden text-gray-500 hover:text-gray-700"
          >
            <Menu className="h-6 w-6" />
          </button>

          {/* Notifications */}
          <div className="notifications-container relative mr-4">
            <button
              onClick={handleNotificationsClick}
              className="relative p-1 rounded-full text-gray-500 hover:text-gray-700 hover:bg-gray-100"
            >
              <Bell className="h-6 w-6" />
              {unreadCount > 0 && (
                <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </span>
              )}
            </button>

            {/* Notifications dropdown */}
            {isNotificationsOpen && (
              <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg py-1 z-50">
                <div className="px-4 py-2 border-b flex justify-between items-center">
                  <h3 className="text-sm font-medium">Notifications</h3>
                  {unreadCount > 0 && (
                    <button
                      onClick={markAllAsRead}
                      className="text-xs text-primary-600 hover:text-primary-800"
                    >
                      Mark all as read
                    </button>
                  )}
                </div>

                <div className="max-h-80 overflow-y-auto">
                  {loading ? (
                    <div className="p-4 text-center text-sm text-gray-500">Loading...</div>
                  ) : notifications.length > 0 ? (
                    notifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`px-4 py-3 border-b hover:bg-gray-50 ${!notification.read ? 'bg-blue-50' : ''}`}
                      >
                        <div className="flex justify-between items-start">
                          <h4 className="text-sm font-medium">{notification.title}</h4>
                          <span className="text-xs text-gray-500">
                            {new Date(notification.created_at).toLocaleDateString()}
                          </span>
                        </div>
                        <p className="text-xs text-gray-600 mt-1">{notification.content}</p>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center text-sm text-gray-500">No notifications</div>
                  )}
                </div>

                <div className="px-4 py-2 border-t">
                  <Link href="/admin/notifications" className="text-xs text-primary-600 hover:text-primary-800">
                    View all notifications
                  </Link>
                </div>
              </div>
            )}
          </div>

          {/* User dropdown */}
          <div className="user-dropdown-container relative ml-auto">
            <button
              onClick={() => setUserDropdownOpen(!isUserDropdownOpen)}
              className="flex items-center space-x-2 text-sm text-gray-700 hover:text-gray-900"
            >
              <div className="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center">
                <span className="text-primary-700 font-medium">
                  {user ? getUserInitials(user) : '??'}
                </span>
              </div>
              <span>{user?.email || 'Loading...'}</span>
              <ChevronDown className="h-4 w-4" />
            </button>

            {/* Dropdown menu */}
            {isUserDropdownOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                <button
                  onClick={handleLogout}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  Logout
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Page content */}
        <main className="p-4">
          {children}
        </main>
      </div>
    </div>
  );
}