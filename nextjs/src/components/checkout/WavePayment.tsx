import React, { useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Copy, CheckCircle } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface WavePaymentProps {
  amount: number;
  currency: string;
  onProceed: () => void;
}

const WavePayment: React.FC<WavePaymentProps> = ({ amount, currency, onProceed }) => {
  const [copied, setCopied] = useState(false);
  const waveNumber = '+220 123 4567'; // Replace with your actual Wave number
  
  // This would be your actual Wave QR code image
  const qrCodeUrl = '/images/wave-qr-placeholder.png'; 

  const handleCopyNumber = () => {
    navigator.clipboard.writeText(waveNumber);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-xl font-semibold mb-4">Pay with Wave</h2>
      
      <Alert className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Please send exactly {formatCurrency(amount, currency)} to our Wave account.
        </AlertDescription>
      </Alert>
      
      <div className="flex flex-col items-center">
        <div className="mb-6 p-4 border border-gray-200 rounded-lg">
          {/* This is a placeholder. In production, use your actual Wave QR code */}
          <div className="relative w-64 h-64 mx-auto bg-gray-100 flex items-center justify-center">
            <p className="text-gray-500 text-sm">QR Code Placeholder</p>
            {/* Uncomment when you have the actual QR code image */}
            {/* <Image
              src={qrCodeUrl}
              alt="Wave QR Code"
              fill
              className="object-contain"
            /> */}
          </div>
        </div>
        
        <div className="w-full max-w-md mb-6">
          <p className="text-center font-medium mb-2">Or send to our Wave number:</p>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
            <span className="font-mono">{waveNumber}</span>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleCopyNumber}
              className="flex items-center gap-1"
            >
              {copied ? (
                <>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-green-500">Copied</span>
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4" />
                  <span>Copy</span>
                </>
              )}
            </Button>
          </div>
        </div>
        
        <div className="w-full max-w-md space-y-4">
          <div className="p-4 bg-blue-50 rounded-md">
            <h3 className="font-medium text-blue-800 mb-2">Payment Instructions:</h3>
            <ol className="list-decimal pl-5 text-blue-700 space-y-1">
              <li>Open your Wave app</li>
              <li>Scan the QR code above or send to our number</li>
              <li>Send exactly {formatCurrency(amount, currency)}</li>
              <li>After payment, click &quot;I&apos;ve Paid&quot; below</li>
              <li>You'll need to provide your Wave Transaction ID</li>
            </ol>
          </div>
          
          <Button 
            onClick={onProceed}
            className="w-full bg-primary-600 hover:bg-primary-700"
          >
            I&apos;ve Paid
          </Button>
          
          <p className="text-center text-sm text-gray-500">
            Please only click &quot;I&apos;ve Paid&quot; after you have completed the payment.
          </p>
        </div>
      </div>
    </div>
  );
};

export default WavePayment;
