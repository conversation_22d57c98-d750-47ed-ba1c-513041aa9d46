import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Copy, CheckCircle, Building2, AlertCircle } from 'lucide-react';
import { formatCurrency } from '@/utils';

interface BankTransferPaymentProps {
  amount: number;
  currency: string;
  onProceed: () => void;
}

// Bank account details - in production, this would come from your backend
const bankDetails = {
  bankName: 'Trust Bank Limited',
  accountName: 'Finder Marketplace Ltd',
  accountNumber: '*********0',
  routingNumber: '*********',
  swiftCode: 'TBLGMGM1',
  branch: 'Kairaba Avenue Branch'
};

const BankTransferPayment: React.FC<BankTransferPaymentProps> = ({ amount, currency, onProceed }) => {
  const [copiedField, setCopiedField] = useState<string | null>(null);

  const handleCopy = (text: string, field: string) => {
    navigator.clipboard.writeText(text);
    setCopiedField(field);
    setTimeout(() => setCopiedField(null), 2000);
  };

  const CopyButton = ({ text, field }: { text: string; field: string }) => (
    <Button 
      variant="ghost" 
      size="sm" 
      onClick={() => handleCopy(text, field)}
      className="flex items-center gap-1 flex-shrink-0 ml-2"
    >
      {copiedField === field ? (
        <>
          <CheckCircle className="h-4 w-4 text-green-500" />
          <span className="text-green-500 text-xs">Copied</span>
        </>
      ) : (
        <>
          <Copy className="h-4 w-4" />
          <span className="text-xs">Copy</span>
        </>
      )}
    </Button>
  );

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <div className="flex items-center gap-3 mb-4">
        <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
          <Building2 className="h-6 w-6 text-green-600" />
        </div>
        <h2 className="text-xl font-semibold">Bank Transfer Payment</h2>
      </div>
      
      <Alert className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Please transfer exactly <strong>{formatCurrency(amount, currency)}</strong> to the bank account below.
        </AlertDescription>
      </Alert>

      <div className="bg-gray-50 p-6 rounded-lg mb-6">
        <h3 className="font-semibold text-lg mb-4 text-gray-800">Bank Account Details</h3>
        
        <div className="space-y-4">
          <div className="flex justify-between items-center py-2 border-b border-gray-200">
            <span className="font-medium text-gray-600">Bank Name:</span>
            <div className="flex items-center">
              <span className="font-mono text-sm">{bankDetails.bankName}</span>
              <CopyButton text={bankDetails.bankName} field="bankName" />
            </div>
          </div>
          
          <div className="flex justify-between items-center py-2 border-b border-gray-200">
            <span className="font-medium text-gray-600">Account Name:</span>
            <div className="flex items-center">
              <span className="font-mono text-sm">{bankDetails.accountName}</span>
              <CopyButton text={bankDetails.accountName} field="accountName" />
            </div>
          </div>
          
          <div className="flex justify-between items-center py-2 border-b border-gray-200">
            <span className="font-medium text-gray-600">Account Number:</span>
            <div className="flex items-center">
              <span className="font-mono text-sm font-bold">{bankDetails.accountNumber}</span>
              <CopyButton text={bankDetails.accountNumber} field="accountNumber" />
            </div>
          </div>
          
          <div className="flex justify-between items-center py-2 border-b border-gray-200">
            <span className="font-medium text-gray-600">Routing Number:</span>
            <div className="flex items-center">
              <span className="font-mono text-sm">{bankDetails.routingNumber}</span>
              <CopyButton text={bankDetails.routingNumber} field="routingNumber" />
            </div>
          </div>
          
          <div className="flex justify-between items-center py-2 border-b border-gray-200">
            <span className="font-medium text-gray-600">SWIFT Code:</span>
            <div className="flex items-center">
              <span className="font-mono text-sm">{bankDetails.swiftCode}</span>
              <CopyButton text={bankDetails.swiftCode} field="swiftCode" />
            </div>
          </div>
          
          <div className="flex justify-between items-center py-2">
            <span className="font-medium text-gray-600">Branch:</span>
            <div className="flex items-center">
              <span className="font-mono text-sm">{bankDetails.branch}</span>
              <CopyButton text={bankDetails.branch} field="branch" />
            </div>
          </div>
        </div>
      </div>

      <div className="bg-blue-50 p-4 rounded-md mb-6">
        <h3 className="font-medium text-blue-800 mb-2">Payment Instructions:</h3>
        <ol className="list-decimal pl-5 text-blue-700 space-y-1 text-sm">
          <li>Open your mobile banking app or visit your bank</li>
          <li>Select &quot;Transfer&quot; or &quot;Send Money&quot;</li>
          <li>Enter the account details above</li>
          <li>Transfer exactly <strong>{formatCurrency(amount, currency)}</strong></li>
          <li>Save your transaction reference/receipt</li>
          <li>Click &quot;I&apos;ve Transferred&quot; below</li>
          <li>Enter your transaction reference when prompted</li>
        </ol>
      </div>

      <div className="bg-yellow-50 p-4 rounded-md mb-6">
        <h3 className="font-medium text-yellow-800 mb-2">Important Notes:</h3>
        <ul className="list-disc pl-5 text-yellow-700 space-y-1 text-sm">
          <li>Transfer the exact amount: <strong>{formatCurrency(amount, currency)}</strong></li>
          <li>Include your order reference in the transfer description if possible</li>
          <li>Bank transfers may take 1-3 business days to process</li>
          <li>Keep your transaction receipt for verification</li>
          <li>Contact us if you encounter any issues</li>
        </ul>
      </div>

      <Button 
        onClick={onProceed}
        className="w-full bg-green-600 hover:bg-green-700 text-white py-3"
      >
        I&apos;ve Transferred the Money
      </Button>
      
      <p className="text-center text-sm text-gray-500 mt-4">
        Please only click &quot;I&apos;ve Transferred&quot; after you have completed the bank transfer.
      </p>
    </div>
  );
};

export default BankTransferPayment;