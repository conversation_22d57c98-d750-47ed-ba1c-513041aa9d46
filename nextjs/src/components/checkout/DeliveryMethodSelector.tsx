import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Truck, MapPin, AlertCircle, Plane } from "lucide-react";
import { formatCurrency } from "@/utils";
import { CheckoutService } from "@/lib/services/checkout";
import { ExternalDeliveryService } from "@/features/admin/types";
import { CourierService } from "@/features/products/types";

interface DeliveryMethodSelectorProps {
  selectedMethod: "delivery" | "pickup" | "courier";
  onMethodChange: (method: "delivery" | "pickup" | "courier") => void;
  deliveryProviders?: ExternalDeliveryService[];
  courierServices?: CourierService[];
  selectedProviderId?: string | null;
  onProviderSelect?: (providerId: string) => void;
  onContinue?: () => void; // Add continue button callback
  className?: string;
  isInternationalProduct?: boolean; // Add flag to determine if product is international
}

const DeliveryMethodSelector: React.FC<DeliveryMethodSelectorProps> = ({
  selectedMethod,
  onMethodChange,
  deliveryProviders = [],
  courierServices = [],
  selectedProviderId,
  onProviderSelect,
  onContinue,
  className = "",
  isInternationalProduct = false,
}) => {
  // const deliveryFee = CheckoutService.calculateDeliveryFee('delivery'); // This will be dynamic now
  const pickupFee = CheckoutService.calculateDeliveryFee("pickup");

  // Determine which services to show based on selected method and product type
  const getActiveServices = () => {
    if (isInternationalProduct) {
      // For international products, only courier services are available
      return courierServices || [];
    } else {
      // For local/domestic products, only show delivery providers (external delivery services)
      // Courier services are not available for local products
      return deliveryProviders || [];
    }
  };

  const activeServices = getActiveServices();
  const serviceType = selectedMethod === 'courier' ? 'Courier' : 'Delivery';
  const serviceIcon = selectedMethod === 'courier' ? Plane : Truck;

  const canContinue = selectedMethod === 'pickup' || 
                     ((selectedMethod === 'delivery' || selectedMethod === 'courier') && selectedProviderId);



  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Choose Delivery Method</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <RadioGroup
          value={selectedMethod}
          onValueChange={(value) => {
            onMethodChange(value as "delivery" | "pickup" | "courier");
          }}
          className="space-y-4"
        >
          {/* International Product: Only show courier option */}
          {isInternationalProduct ? (
            <div className="space-y-4">
              <Label
                htmlFor="courier"
                className={`flex items-center space-x-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer ${
                  selectedMethod === 'courier' ? 'border-primary-500 bg-primary-50' : 'border-gray-200'
                }`}
              >
                <RadioGroupItem value="courier" id="courier" />
                <div className="flex items-center space-x-3 flex-1">
                  <Plane className="h-5 w-5 text-primary-600" />
                  <div className="flex-1">
                    <span className="text-base font-medium">International Shipping</span>
                    <p className="text-sm text-gray-500">
                      Get your order shipped internationally via courier service
                    </p>
                  </div>
                  <div className="text-right">
                    <span className="text-base font-medium text-primary-600">
                      {selectedMethod === "courier" && selectedProviderId
                        ? formatCurrency(
                            activeServices.find(p => p.id === selectedProviderId)?.base_fee || 
                            activeServices.find(p => p.id === selectedProviderId)?.base_cost || 0,
                            "GMD"
                          )
                        : "Select Courier"}
                    </span>
                  </div>
                </div>
              </Label>
              
              {/* Courier Services - Only show when courier is selected */}
              {selectedMethod === "courier" && (
                <div className="ml-8 space-y-3">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">
                    Choose a Courier Service:
                  </h4>
                  {activeServices.length > 0 ? (
                    <div className="space-y-2">
                      {activeServices.map((service) => {
                        const serviceName = service.name;
                        const serviceId = service.id;
                        const estimatedTime = service.estimated_delivery_time;
                        const serviceCost = service.base_fee || service.base_cost || 0;
                        const serviceLogo = service.logo;

                        return (
                          <div
                            key={serviceId}
                            onClick={() => onProviderSelect && onProviderSelect(serviceId)}
                            className={`flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                              selectedProviderId === serviceId
                                ? "bg-primary-50 border-primary-300 ring-2 ring-primary-200"
                                : "hover:bg-gray-50"
                            }`}
                          >
                            {serviceLogo ? (
                              <img
                                src={serviceLogo}
                                alt={serviceName}
                                className="w-8 h-8 rounded-full object-cover"
                              />
                            ) : (
                              <Plane className="h-6 w-6 text-gray-400" />
                            )}
                            <div className="flex-1">
                              <p className="text-sm font-medium">{serviceName}</p>
                              {estimatedTime && (
                                <p className="text-xs text-gray-500">
                                  Est. Shipping: {estimatedTime}
                                </p>
                              )}
                              {service.countries && (
                                <p className="text-xs text-blue-600">
                                  Ships to: {service.countries.slice(0, 3).join(', ')}
                                  {service.countries.length > 3 && ` +${service.countries.length - 3} more`}
                                </p>
                              )}
                            </div>
                            <div className="text-right">
                              <span className="text-sm font-medium text-gray-800">
                                {formatCurrency(serviceCost, "GMD")}
                                {service.costPerKg && (
                                  <span className="block text-xs text-gray-500">
                                    +{formatCurrency(service.costPerKg, "GMD")}/kg
                                  </span>
                                )}
                              </span>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <p className="text-sm text-yellow-800">
                        <AlertCircle className="h-4 w-4 inline mr-1" />
                        No courier services available at the moment.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            /* Domestic Product: Show delivery and pickup options only */
            <>
              {/* Home Delivery Option */}
              <div className="space-y-4">
                <Label
                  htmlFor="delivery"
                  className={`flex items-center space-x-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer ${
                    selectedMethod === 'delivery' ? 'border-primary-500 bg-primary-50' : 'border-gray-200'
                  }`}
                >
                  <RadioGroupItem value="delivery" id="delivery" />
                  <div className="flex items-center space-x-3 flex-1">
                    <Truck className="h-5 w-5 text-primary-600" />
                    <div className="flex-1">
                      <span className="text-base font-medium">Home Delivery</span>
                      <p className="text-sm text-gray-500">
                        Get your order delivered to your doorstep by local delivery service
                      </p>
                    </div>
                    <div className="text-right">
                      <span className="text-base font-medium text-primary-600">
                        {selectedMethod === "delivery" && selectedProviderId
                          ? formatCurrency(
                              activeServices.find(p => p.id === selectedProviderId)?.base_fee || 
                              activeServices.find(p => p.id === selectedProviderId)?.base_cost || 0,
                              "GMD"
                            )
                          : "Select Service"}
                      </span>
                    </div>
                  </div>
                </Label>

                {/* Delivery Service Providers - Only show when delivery is selected */}
                {selectedMethod === "delivery" && (
                  <div className="ml-8 space-y-3">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">
                      Choose a Delivery Service:
                    </h4>
                    {activeServices.length > 0 ? (
                      <div className="space-y-2">
                        {activeServices.map((service) => {
                          const serviceName = service.name;
                          const serviceId = service.id;
                          // For delivery services (ExternalDeliveryService), use estimated_delivery_time
                          const estimatedTime = (service as any).estimated_delivery_time;
                          // For delivery services, use base_fee
                          const serviceCost = (service as any).base_fee || 0;
                          const serviceLogo = (service as any).logo;

                          return (
                            <div
                              key={serviceId}
                              onClick={() => onProviderSelect && onProviderSelect(serviceId)}
                              className={`flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                                selectedProviderId === serviceId
                                  ? "bg-primary-50 border-primary-300 ring-2 ring-primary-200"
                                  : "hover:bg-gray-50"
                              }`}
                            >
                              {serviceLogo ? (
                                <img
                                  src={serviceLogo}
                                  alt={serviceName}
                                  className="w-8 h-8 rounded-full object-cover"
                                />
                              ) : (
                                <Truck className="h-6 w-6 text-gray-400" />
                              )}
                              <div className="flex-1">
                                <p className="text-sm font-medium">{serviceName}</p>
                                {estimatedTime && (
                                  <p className="text-xs text-gray-500">
                                    Est. Delivery: {estimatedTime}
                                  </p>
                                )}
                              </div>
                              <div className="text-right">
                                <span className="text-sm font-medium text-gray-800">
                                  {formatCurrency(serviceCost, "GMD")}
                                </span>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    ) : (
                      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <p className="text-sm text-yellow-800">
                          <AlertCircle className="h-4 w-4 inline mr-1" />
                          No delivery services available at the moment.
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Pickup Option - Only for domestic products */}
              <Label
                htmlFor="pickup"
                className={`flex items-center space-x-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors cursor-pointer ${
                  selectedMethod === 'pickup' ? 'border-primary-500 bg-primary-50' : 'border-gray-200'
                }`}
              >
                <RadioGroupItem value="pickup" id="pickup" />
                <div className="flex items-center space-x-3 flex-1">
                  <MapPin className="h-5 w-5 text-primary-600" />
                  <div className="flex-1">
                    <span className="text-base font-medium">Store Pickup</span>
                    <p className="text-sm text-gray-500">
                      Pick up your order from the store location
                    </p>
                  </div>
                  <div className="text-right">
                    <span className="text-base font-medium text-green-600">
                      {pickupFee === 0 ? "Free" : formatCurrency(pickupFee, "GMD")}
                    </span>
                  </div>
                </div>
              </Label>
            </>
          )}
        </RadioGroup>

        {/* Additional Information */}
        {selectedMethod === "pickup" && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Pickup Instructions</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• You will receive store location details after order confirmation</li>
              <li>• <strong>Bring your order receipt</strong> (available in Order History)</li>
              <li>• Present a valid ID when collecting your order</li>
              <li>• Orders are typically ready within 2-4 hours</li>
            </ul>
          </div>
        )}

        {(selectedMethod === "delivery" || selectedMethod === "courier") && selectedProviderId && activeServices.length > 0 && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <h4 className="font-medium text-green-900 mb-2">
              {selectedMethod === "courier" || isInternationalProduct ? 'Shipping Details' : 'Delivery Details'}
            </h4>
            <p className="text-sm text-green-800">
              Your order will be {selectedMethod === "courier" || isInternationalProduct ? 'shipped' : 'delivered'} by {activeServices.find(s => s.id === selectedProviderId)?.name}.
              {(() => {
                const selectedService = activeServices.find(s => s.id === selectedProviderId);
                const estimatedTime = isInternationalProduct
                  ? (selectedService as any)?.estimated_delivery_time
                  : (selectedService as any)?.estimated_delivery_time;
                return estimatedTime && (
                  <span> Estimated {selectedMethod === "courier" || isInternationalProduct ? 'shipping' : 'delivery'} time: {estimatedTime}</span>
                );
              })()}
              {(() => {
                const selectedService = activeServices.find(s => s.id === selectedProviderId);
                const trackingSupported = isInternationalProduct && (selectedService as any)?.tracking_supported;
                return trackingSupported && (
                  <span> Tracking is supported for this service.</span>
                );
              })()}
            </p>
          </div>
        )}

        {/* Continue Button */}
        <div className="flex justify-between items-center pt-4 border-t">
          <Button
            variant="outline"
            onClick={() => window.history.back()}
          >
            Back to Cart
          </Button>
          <Button
            onClick={onContinue}
            disabled={!canContinue}
            className="bg-primary-600 hover:bg-primary-700"
          >
            Continue to {selectedMethod === 'pickup' ? 'Payment' : 'Address'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default DeliveryMethodSelector;
