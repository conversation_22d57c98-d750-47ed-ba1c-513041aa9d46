"use client";
import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import { useGlobal } from '@/lib/context/GlobalContext';
import { createSPASassClient } from '@/lib/supabase/client';
import {
    Key,
    User,
    CheckCircle,
    Shield,
    Mail,
    Phone,
    ArrowLeft
} from 'lucide-react';
import { MFASetup } from '@/components/MFASetup';
import ProfileForm from '@/components/profile/ProfileForm';
import Link from 'next/link';

export default function MyAccountPage() {
    const { user } = useGlobal();
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [userProfile, setUserProfile] = useState<any>(null);

    // Fetch user profile
    useEffect(() => {
        if (user) {
            fetchUserProfile();
        }
    }, [user]);

    const fetchUserProfile = async () => {
        try {
            const supabase = await createSPASassClient();
            const { data: profile, error } = await supabase.getSupabaseClient()
                .from('profiles')
                .select('*')
                .eq('id', user!.id || '')
                .single();

            if (error) throw error;
            setUserProfile(profile);
        } catch (err) {
            console.error('Error fetching profile:', err);
        }
    };

    const handlePasswordChange = async (e: React.FormEvent) => {
        e.preventDefault();
        if (newPassword !== confirmPassword) {
            setError("New passwords don't match");
            return;
        }

        setLoading(true);
        setError('');
        setSuccess('');

        try {
            const supabase = await createSPASassClient();
            const client = supabase.getSupabaseClient();

            const { error } = await client.auth.updateUser({
                password: newPassword
            });

            if (error) throw error;

            setSuccess('Password updated successfully');
            setNewPassword('');
            setConfirmPassword('');
        } catch (err: Error | unknown) {
            if (err instanceof Error) {
                console.error('Error updating password:', err);
                setError(err.message);
            } else {
                console.error('Error updating password:', err);
                setError('Failed to update password');
            }
        } finally {
            setLoading(false);
        }
    };

    if (!user) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center p-6">
                <Card className="w-full max-w-md">
                    <CardHeader className="text-center">
                        <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <User className="h-8 w-8 text-primary-600" />
                        </div>
                        <CardTitle className="text-2xl">Welcome Back</CardTitle>
                        <CardDescription>
                            Please log in to access your account profile
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="text-center">
                        <Link href="/auth/login">
                            <Button className="w-full bg-primary-600 hover:bg-primary-700">
                                <User className="w-4 h-4 mr-2" />
                                Log In
                            </Button>
                        </Link>
                        <p className="text-sm text-gray-500 mt-4">
                            Don&apos;t have an account?{' '}
                            <Link href="/auth/register" className="text-primary-600 hover:underline">
                                Sign up
                            </Link>
                        </p>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <div className="bg-white shadow-sm">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <Link href="/" className="text-gray-500 hover:text-gray-700">
                                <ArrowLeft className="h-5 w-5" />
                            </Link>
                            <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                                <User className="h-6 w-6 text-primary-600" />
                            </div>
                            <div>
                                <h1 className="text-2xl font-bold text-gray-900">
                                    My Profile
                                </h1>
                                <p className="text-gray-600">
                                    Manage your personal information and security settings
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {error && (
                    <Alert variant="destructive" className="mb-6">
                        <AlertDescription>{error}</AlertDescription>
                    </Alert>
                )}

                {success && (
                    <Alert className="mb-6">
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>{success}</AlertDescription>
                    </Alert>
                )}

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Left Column - Profile Form */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Profile Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Profile Information</CardTitle>
                                <CardDescription>Update your personal information and contact details</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <ProfileForm />
                            </CardContent>
                        </Card>

                        {/* Security Settings */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Shield className="h-5 w-5" />
                                    Security Settings
                                </CardTitle>
                                <CardDescription>Manage your password and security preferences</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                {/* Password Change */}
                                <div>
                                    <h3 className="font-medium mb-3 flex items-center gap-2">
                                        <Key className="h-4 w-4" />
                                        Change Password
                                    </h3>
                                    <form onSubmit={handlePasswordChange} className="space-y-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                New Password
                                            </label>
                                            <input
                                                type="password"
                                                placeholder="Enter new password"
                                                value={newPassword}
                                                onChange={(e) => setNewPassword(e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                                required
                                            />
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Confirm New Password
                                            </label>
                                            <input
                                                type="password"
                                                placeholder="Confirm new password"
                                                value={confirmPassword}
                                                onChange={(e) => setConfirmPassword(e.target.value)}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                                required
                                            />
                                        </div>
                                        <Button
                                            type="submit"
                                            disabled={loading}
                                            size="sm"
                                        >
                                            {loading ? 'Updating...' : 'Update Password'}
                                        </Button>
                                    </form>
                                </div>

                                {/* MFA Setup */}
                                <div className="pt-6 border-t">
                                    <h3 className="font-medium mb-3">Two-Factor Authentication</h3>
                                    <p className="text-sm text-gray-600 mb-4">
                                        Add an extra layer of security to your account
                                    </p>
                                    <MFASetup />
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Right Column - Account Summary */}
                    <div className="space-y-6">
                        {/* Account Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <User className="h-5 w-5" />
                                    Account Summary
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="flex items-center gap-3">
                                    <Mail className="h-4 w-4 text-gray-400" />
                                    <div>
                                        <p className="text-sm font-medium">Email</p>
                                        <p className="text-sm text-gray-600">{user?.email}</p>
                                    </div>
                                </div>
                                {userProfile?.phone && (
                                    <div className="flex items-center gap-3">
                                        <Phone className="h-4 w-4 text-gray-400" />
                                        <div>
                                            <p className="text-sm font-medium">Phone</p>
                                            <p className="text-sm text-gray-600">{userProfile.phone}</p>
                                        </div>
                                    </div>
                                )}
                                {/* <div className="flex items-center gap-3">
                                    <Calendar className="h-4 w-4 text-gray-400" />
                                    <div>
                                        <p className="text-sm font-medium">Member Since</p>
                                        <p className="text-sm text-gray-600">
                                            {new Date(user?.created_at || '').toLocaleDateString()}
                                        </p>
                                    </div>
                                </div> */}
                                {userProfile?.first_name && (
                                    <div className="flex items-center gap-3">
                                        <User className="h-4 w-4 text-gray-400" />
                                        <div>
                                            <p className="text-sm font-medium">Name</p>
                                            <p className="text-sm text-gray-600">
                                                {`${userProfile.first_name} ${userProfile.last_name || ''}`.trim()}
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Quick Links */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Quick Links</CardTitle>
                                <CardDescription>Access other account features</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <Link
                                    href="/account/addresses"
                                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                                >
                                    <span className="text-sm font-medium">Manage Addresses</span>
                                    <ArrowLeft className="h-4 w-4 rotate-180 text-gray-400" />
                                </Link>
                                <Link
                                    href="/my-orders"
                                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                                >
                                    <span className="text-sm font-medium">My Orders</span>
                                    <ArrowLeft className="h-4 w-4 rotate-180 text-gray-400" />
                                </Link>
                                <Link
                                    href="/wishlist"
                                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors"
                                >
                                    <span className="text-sm font-medium">Wishlist</span>
                                    <ArrowLeft className="h-4 w-4 rotate-180 text-gray-400" />
                                </Link>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </div>
    );
}