'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Heart, ShoppingCart, Trash2, ArrowLeft } from 'lucide-react';
import EcommerceLayout from '@/components/ecommerce/EcommerceLayout';
import { Button } from '@/components/ui/button';
import { useToast } from '@/lib/hooks/use-toast';
import { useCart } from '@/lib/context/CartContext';
import { createSPASassClient } from '@/lib/supabase/client';
import { formatCurrency } from '@/lib/utils';
import { WishlistItem } from '@/lib/types/ecommerce';

export default function WishlistPage() {
  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const { toast } = useToast();
  const { addToCart } = useCart();

  useEffect(() => {
    fetchWishlist();
  }, []);

  const fetchWishlist = async () => {
    try {
      setLoading(true);
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        setIsAuthenticated(false);
        setLoading(false);
        return;
      }

      setIsAuthenticated(true);

      const { data, error } = await supabase
        .from('wishlist')
        .select(`
          *,
          product:products(
            *,
            images:product_images(url),
            store:stores(name, slug),
            category:categories(name, slug)
          )
        `)
        .eq('user_id', user.id);

      if (error) throw error;
      setWishlistItems(data || []);
    } catch (error) {
      console.error('Error fetching wishlist:', error);
      toast({
        title: "Error",
        description: "Failed to load wishlist items.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const removeFromWishlist = async (wishlistItemId: string) => {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { error } = await supabase
        .from('wishlist')
        .delete()
        .eq('id', wishlistItemId);

      if (error) throw error;

      setWishlistItems(prev => prev.filter(item => item.id !== wishlistItemId));
      toast({
        title: "Removed from wishlist",
        description: "Item has been removed from your wishlist.",
        variant: "success",
      });
    } catch (error) {
      console.error('Error removing from wishlist:', error);
      toast({
        title: "Error",
        description: "Failed to remove item from wishlist.",
        variant: "destructive",
      });
    }
  };

  const handleAddToCart = async (productId: string, productName: string) => {
    try {
      await addToCart(productId, 1);
      toast({
        title: "Added to cart",
        description: `${productName} has been added to your cart.`,
        variant: "success",
      });
    } catch (error) {
      console.error('Error adding to cart:', error);
      toast({
        title: "Error",
        description: "Failed to add item to cart. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (!isAuthenticated) {
    return (
      <EcommerceLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <Heart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Sign in to view your wishlist</h2>
            <p className="text-gray-600 mb-6">
              Create an account or sign in to save your favorite items.
            </p>
            <div className="space-x-4">
              <Link href="/auth/login">
                <Button>Sign In</Button>
              </Link>
              <Link href="/auth/register">
                <Button variant="outline">Create Account</Button>
              </Link>
            </div>
          </div>
        </div>
      </EcommerceLayout>
    );
  }

  if (loading) {
    return (
      <EcommerceLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="bg-gray-200 rounded-lg h-64"></div>
              ))}
            </div>
          </div>
        </div>
      </EcommerceLayout>
    );
  }

  return (
    <EcommerceLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <Heart className="h-8 w-8 text-red-500" />
            <h1 className="text-3xl font-bold text-gray-900">My Wishlist</h1>
          </div>
          <p className="text-gray-600">
            {wishlistItems.length} {wishlistItems.length === 1 ? 'item' : 'items'} saved for later
          </p>
        </div>

        {wishlistItems.length === 0 ? (
          <div className="text-center py-12">
            <Heart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Your wishlist is empty</h2>
            <p className="text-gray-600 mb-6">
              Start browsing and save items you love to your wishlist.
            </p>
            <Link href="/products">
              <Button>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Continue Shopping
              </Button>
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {wishlistItems.map((item) => {
              const product = item.product;
              if (!product) return null;

              const imageUrl = product.images?.[0]?.url || '/placeholder-product.jpg';
              const discount = product.compare_at_price 
                ? Math.round(((product.compare_at_price - product.price) / product.compare_at_price) * 100) 
                : 0;

              return (
                <div key={item.id} className="group relative bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                  {/* Discount badge */}
                  {discount > 0 && (
                    <div className="absolute top-2 left-2 z-10 bg-red-500 text-white text-xs font-medium px-2 py-1 rounded">
                      {discount}% OFF
                    </div>
                  )}

                  {/* Remove from wishlist button */}
                  <button
                    onClick={() => removeFromWishlist(item.id)}
                    className="absolute top-2 right-2 z-10 bg-white/80 p-1.5 rounded-full text-red-500 hover:text-red-700 hover:bg-white transition-colors"
                    aria-label="Remove from wishlist"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>

                  {/* Product image */}
                  <Link href={`/products/${product.slug}`} className="block relative h-48 md:h-56 lg:h-64 bg-gray-100">
                    <Image
                      src={imageUrl}
                      alt={product.name}
                      fill
                      sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
                      className="object-cover object-center group-hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/placeholder-product.jpg';
                      }}
                    />
                  </Link>

                  {/* Product info */}
                  <div className="p-4">
                    {/* Store name */}
                    {product.store && (
                      <Link href={`/stores/${product.store.slug}`} className="text-xs text-gray-500 hover:text-primary-600 mb-1 block">
                        {product.store.name}
                      </Link>
                    )}

                    {/* Product name */}
                    <Link href={`/products/${product.slug}`} className="block">
                      <h3 className="text-sm font-medium text-gray-900 line-clamp-2 mb-2 hover:text-primary-600 transition-colors">
                        {product.name}
                      </h3>
                    </Link>

                    {/* Price */}
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <span className="text-sm font-medium text-gray-900">
                          {formatCurrency(product.price, product.currency || 'GMD')}
                        </span>
                        {product.compare_at_price && product.compare_at_price > product.price && (
                          <span className="text-xs text-gray-500 line-through ml-1">
                            {formatCurrency(product.compare_at_price, product.currency || 'GMD')}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Add to cart button */}
                    <Button
                      onClick={() => handleAddToCart(product.id, product.name)}
                      className="w-full"
                      size="sm"
                    >
                      <ShoppingCart className="w-4 h-4 mr-2" />
                      Add to Cart
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </EcommerceLayout>
  );
}
