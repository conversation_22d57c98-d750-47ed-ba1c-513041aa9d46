"use client";
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { AdminHeader } from '@/components/admin';
import { CreateProductParams, Category } from '@/features/admin/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Loader2, AlertCircle, Plus } from 'lucide-react';
import { slugify } from '@/lib/utils/string';
import { useAuth } from '@/lib/hooks/useAuth';
import { useToast } from '@/lib/hooks/use-toast';
interface Vendor {
  id: string;
  name: string;
  status: string;
}

export default function NewProductPage() {
  const router = useRouter();
  const { role } = useAuth();
  const { toast } = useToast();
  const [categories, setCategories] = useState<Category[]>([]);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showVendorModal, setShowVendorModal] = useState(false);
  const [creatingVendor, setCreatingVendor] = useState(false);
  const [newVendorData, setNewVendorData] = useState({
    name: '',
    contact_email: '',
    contact_phone: '',
    commission_rate: 10.00,
    notes: '',
  });
  const [formData, setFormData] = useState<CreateProductParams>({
    name: '',
    slug: '',
    description: '',
    price: 0,
    compare_at_price: 0,
    currency: 'GMD',
    category_id: null,
    vendor_id: '',
    featured: false,
    trending: false,
    in_stock: true,
  });

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Check if we have a vendor parameter in the URL
        const searchParams = new URLSearchParams(window.location.search);
        const vendorParam = searchParams.get('vendor');

        // Fetch categories and vendors separately to handle errors individually
        try {
          // Use the admin API endpoint instead of the client-side function
          const response = await fetch('/api/admin/categories');
          const data = await response.json();

          if (response.ok) {
            setCategories(data);
          } else {
            throw new Error(data.error || 'Failed to fetch categories');
          }
        } catch (categoryError) {
          console.error('Error fetching categories:', categoryError);
          // Don't fail completely, just show empty categories
          setCategories([]);
        }

        try {
          // For admins, get all vendors
          const response = await fetch('/api/admin/vendors');
          const data = await response.json();

          if (data.error) {
            throw new Error(data.error);
          }

          if (data.vendors && data.vendors.length > 0) {
            setVendors(data.vendors.filter((vendor: Vendor) => vendor.status === 'active'));

            // If we have a vendor parameter, find and select it
            if (vendorParam) {
              const selectedVendor = data.vendors.find((vendor: Vendor) =>
                vendor.name.toLowerCase().includes(vendorParam.toLowerCase())
              );

              if (selectedVendor) {
                setFormData(prev => ({ ...prev, vendor_id: selectedVendor.id }));
              }
            }
          } else {
            setVendors([]);
          }
        } catch (vendorError) {
          console.error('Error fetching vendors:', vendorError);
          // Don't fail completely, just show empty vendors
          setVendors([]);
        }
      } catch (error) {
        console.error('Error in fetchData:', error);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    // Clean up preview URL when component unmounts
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl, role]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Auto-generate slug when name changes
    if (name === 'name') {
      setFormData((prev) => ({ ...prev, slug: slugify(value) }));
    }
  };

  const handleNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: parseFloat(value) || 0 }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  const handleCreateVendor = async () => {
    if (!newVendorData.name.trim()) {
      setError('Vendor name is required');
      return;
    }

    try {
      setCreatingVendor(true);
      setError(null);

      const response = await fetch('/api/admin/vendors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...newVendorData,
          status: 'active',
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create vendor');
      }

      // Add the new vendor to the list
      setVendors(prev => [...prev, data]);

      // Select the new vendor
      setFormData(prev => ({ ...prev, vendor_id: data.id }));

      // Reset modal state
      setNewVendorData({
        name: '',
        contact_email: '',
        contact_phone: '',
        commission_rate: 10.00,
        notes: '',
      });
      setShowVendorModal(false);

      toast({
        title: "Success",
        description: `Vendor "${data.name}" created and selected`,
      });

    } catch (error) {
      console.error('Error creating vendor:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to create vendor',
        variant: "destructive",
      });
    } finally {
      setCreatingVendor(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);

      // Create a preview URL for the selected file
      const fileUrl = URL.createObjectURL(file);
      setPreviewUrl(fileUrl);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    // Handle the "none" value for category_id
    const submissionData = {
      ...formData,
      category_id: formData.category_id === 'none' ? null : formData.category_id
    };

    try {
      // Use the admin API endpoint instead of the client-side function
      const response = await fetch('/api/admin/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create product');
      }

      const newProduct = await response.json();

      // If we have a selected file, upload it as a product image
      if (newProduct && selectedFile) {
        const imageFormData = new FormData();
        imageFormData.append('product_id', newProduct.id);
        imageFormData.append('file', selectedFile);
        imageFormData.append('alt', formData.name);
        imageFormData.append('position', '0');

        const imageResponse = await fetch('/api/admin/product-images', {
          method: 'POST',
          body: imageFormData,
        });

        if (!imageResponse.ok) {
          console.error('Error uploading product image');
          // Continue anyway, the product was created successfully
        }
      }

      if (newProduct) {
        router.push('/admin/products');
        router.refresh();
      } else {
        setError('Failed to create product');
      }
    } catch (error) {
      console.error('Error creating product:', error);
      setError('An error occurred while creating the product');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // If no vendors are available after loading, show a message
  if (!loading && vendors.length === 0) {
    return (
      <div className="space-y-6">
        <AdminHeader
          title="Add New Product"
          description="Create a new product in your marketplace"
          backHref="/admin/products"
        />

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No Vendors Available</AlertTitle>
          <AlertDescription>
            You need to create at least one vendor before you can add products.
            <div className="mt-2">
              <Button onClick={() => router.push('/admin/vendors/new')}>
                Create a Vendor
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Add New Product"
        description="Create a new product in your marketplace"
        backHref="/admin/products"
      />

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Product Information</CardTitle>
                <CardDescription>
                  Enter the product&apos;s basic information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Product Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="slug">Product Slug</Label>
                  <Input
                    id="slug"
                    name="slug"
                    value={formData.slug}
                    onChange={handleChange}
                    required
                  />
                  <p className="text-sm text-gray-500">
                    This will be used in the product&apos;s URL: /products/{formData.slug}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={4}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">Price (GMD)</Label>
                    <Input
                      id="price"
                      name="price"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.price}
                      onChange={handleNumberChange}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="compare_at_price">Compare at Price (GMD)</Label>
                    <Input
                      id="compare_at_price"
                      name="compare_at_price"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.compare_at_price || ''}
                      onChange={handleNumberChange}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="category_id">Category</Label>
                      <Link href="/admin/categories/new">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                        >
                          Add New Category
                        </Button>
                      </Link>
                    </div>
                    <Select
                      value={formData.category_id || undefined}
                      onValueChange={(value) => handleSelectChange('category_id', value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        {categories.length > 0 ? (
                          categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no-categories" disabled>
                            No categories available
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="vendor_id">Vendor</Label>
                      <Dialog open={showVendorModal} onOpenChange={setShowVendorModal}>
                        <DialogTrigger asChild>
                          <Button type="button" variant="outline" size="sm">
                            <Plus className="h-4 w-4 mr-2" />
                            Add New Vendor
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-[425px]">
                          <DialogHeader>
                            <DialogTitle>Add New Vendor</DialogTitle>
                            <DialogDescription>
                              Create a new vendor to assign to this product.
                            </DialogDescription>
                          </DialogHeader>
                          <div className="grid gap-4 py-4">
                            <div className="space-y-2">
                              <Label htmlFor="vendor-name">Vendor Name *</Label>
                              <Input
                                id="vendor-name"
                                value={newVendorData.name}
                                onChange={(e) => setNewVendorData(prev => ({ ...prev, name: e.target.value }))}
                                placeholder="Enter vendor name"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="vendor-email">Contact Email</Label>
                              <Input
                                id="vendor-email"
                                type="email"
                                value={newVendorData.contact_email}
                                onChange={(e) => setNewVendorData(prev => ({ ...prev, contact_email: e.target.value }))}
                                placeholder="<EMAIL>"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="vendor-phone">Contact Phone</Label>
                              <Input
                                id="vendor-phone"
                                value={newVendorData.contact_phone}
                                onChange={(e) => setNewVendorData(prev => ({ ...prev, contact_phone: e.target.value }))}
                                placeholder="+************"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="vendor-commission">Commission Rate (%)</Label>
                              <Input
                                id="vendor-commission"
                                type="number"
                                step="0.01"
                                min="0"
                                max="100"
                                value={newVendorData.commission_rate}
                                onChange={(e) => setNewVendorData(prev => ({ ...prev, commission_rate: parseFloat(e.target.value) || 0 }))}
                                placeholder="10.00"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="vendor-notes">Notes</Label>
                              <Textarea
                                id="vendor-notes"
                                value={newVendorData.notes}
                                onChange={(e) => setNewVendorData(prev => ({ ...prev, notes: e.target.value }))}
                                placeholder="Internal notes about this vendor..."
                                rows={3}
                              />
                            </div>
                          </div>
                          <DialogFooter>
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => setShowVendorModal(false)}
                            >
                              Cancel
                            </Button>
                            <Button
                              type="button"
                              onClick={handleCreateVendor}
                              disabled={creatingVendor || !newVendorData.name.trim()}
                            >
                              {creatingVendor ? (
                                <>
                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                  Creating...
                                </>
                              ) : (
                                'Create Vendor'
                              )}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>
                    <Select
                      value={formData.vendor_id || undefined}
                      onValueChange={(value) => handleSelectChange('vendor_id', value)}
                      required
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a vendor" />
                      </SelectTrigger>
                      <SelectContent>
                        {vendors.length > 0 ? (
                          vendors.map((vendor) => (
                            <SelectItem key={vendor.id} value={vendor.id}>
                              {vendor.name}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no-vendors" disabled>
                            No vendors available
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Product Image</CardTitle>
                <CardDescription>
                  Add an image for your product
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="product-image">Product Image</Label>
                  <Input
                    id="product-image"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                  />
                  <p className="text-sm text-gray-500">
                    Upload an image for your product (JPEG, PNG, WebP)
                  </p>
                </div>

                {previewUrl && (
                  <div className="mt-4 border rounded-md overflow-hidden">
                    <div className="aspect-square relative bg-gray-100">
                      <Image
                        src={previewUrl}
                        alt="Product preview"
                        fill
                        className="object-contain"
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Product Status</CardTitle>
                <CardDescription>
                  Set the product&apos;s visibility and status
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="in_stock"
                    checked={formData.in_stock}
                    onCheckedChange={(checked) => handleSwitchChange('in_stock', checked)}
                  />
                  <Label htmlFor="in_stock">In Stock</Label>
                </div>

                {/* Only show Featured and Trending options for admins */}
                {role === 'admin' && (
                  <>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="featured"
                        checked={formData.featured}
                        onCheckedChange={(checked) => handleSwitchChange('featured', checked)}
                      />
                      <Label htmlFor="featured">Featured Product</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="trending"
                        checked={formData.trending}
                        onCheckedChange={(checked) => handleSwitchChange('trending', checked)}
                      />
                      <Label htmlFor="trending">Trending Product</Label>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="mt-6 flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/admin/products')}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={saving}>
            {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Create Product
          </Button>
        </div>
      </form>
    </div>
  );
}
