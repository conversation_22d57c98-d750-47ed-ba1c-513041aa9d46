"use client";
import React, { useState, useEffect } from 'react';
import { AdminHeader, DataTable, StatusBadge } from '@/components/admin';
import CSVUpload from '@/components/admin/CSVUpload';
import { getProducts } from '@/features/admin/api';
import { AdminProduct, ProductListParams } from '@/features/admin/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Edit, Eye, Star, StarOff, Tag, Zap, Upload } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { formatCurrency } from '@/lib/utils/format';
import { useAuth } from '@/lib/hooks/useAuth';
import Image from 'next/image';

export default function ProductsPage() {
  const { role, user, hasPermission } = useAuth();
  const [products, setProducts] = useState<AdminProduct[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [showCSVUpload, setShowCSVUpload] = useState(false);
  const [params, setParams] = useState<ProductListParams>({
    page: 1,
    per_page: 10,
    sort_by: 'created_at',
    sort_order: 'desc',
  });

  // Removed store owner logic - admin only now

  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      try {
        const { products, count } = await getProducts(params);
        setProducts(products);
        setTotalCount(count);
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [params]);

  const handlePageChange = (page: number) => {
    setParams({ ...params, page });
  };

  const handleSearch = (search: string) => {
    setParams({ ...params, page: 1, search });
  };

  const handleSortChange = (sort_by: string, sort_order: 'asc' | 'desc') => {
    setParams({ ...params, sort_by, sort_order });
  };

  const handleFeaturedFilter = (featured: string) => {
    setParams({
      ...params,
      page: 1,
      featured: featured === 'all' ? undefined : featured === 'true',
    });
  };

  const handleTrendingFilter = (trending: string) => {
    setParams({
      ...params,
      page: 1,
      trending: trending === 'all' ? undefined : trending === 'true',
    });
  };

  const handleInStockFilter = (in_stock: string) => {
    setParams({
      ...params,
      page: 1,
      in_stock: in_stock === 'all' ? undefined : in_stock === 'true',
    });
  };

  const columns = [
    {
      key: 'name',
      header: 'Product',
      cell: (product: AdminProduct) => (
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded-md bg-gray-100 flex items-center justify-center overflow-hidden">
            {/* Product image would go here */}
            <Tag className="h-5 w-5 text-gray-400" />
          </div>
          <div>
            <div className="font-medium">{product.name}</div>
            <div className="text-sm text-gray-500">
              {formatCurrency(product.price, product.currency)}
            </div>
          </div>
        </div>
      ),
      sortable: true,
    },
    {
      key: 'store_name',
      header: 'Store',
      cell: (product: AdminProduct) => <div>{product.store_name}</div>,
      sortable: true,
    },
    {
      key: 'category_name',
      header: 'Category',
      cell: (product: AdminProduct) => <div>{product.category_name || 'Uncategorized'}</div>,
      sortable: true,
    },
    {
      key: 'featured',
      header: 'Featured',
      cell: (product: AdminProduct) => (
        <div>
          {product.featured ? (
            <Star className="h-5 w-5 text-amber-500 fill-amber-500" />
          ) : (
            <StarOff className="h-5 w-5 text-gray-300" />
          )}
        </div>
      ),
      sortable: true,
    },
    {
      key: 'trending',
      header: 'Trending',
      cell: (product: AdminProduct) => (
        <div>
          {product.trending ? (
            <Zap className="h-5 w-5 text-blue-500 fill-blue-500" />
          ) : (
            <Zap className="h-5 w-5 text-gray-300" />
          )}
        </div>
      ),
      sortable: true,
    },
    {
      key: 'in_stock',
      header: 'In Stock',
      cell: (product: AdminProduct) => (
        <StatusBadge status={product.in_stock ? 'active' : 'disabled'} />
      ),
      sortable: true,
    },
    {
      key: 'created_at',
      header: 'Created',
      cell: (product: AdminProduct) => (
        <div>{new Date(product.created_at).toLocaleDateString()}</div>
      ),
      sortable: true,
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (product: AdminProduct) => (
        <div className="flex items-center gap-2">
          <Link href={`/admin/products/${product.id}`}>
            <Button variant="ghost" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <Link href={`/admin/products/${product.id}/edit`}>
            <Button variant="ghost" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      ),
    },
  ];

  const handleCSVUploadComplete = (result: any) => {
    if (result.success > 0) {
      // Refresh the products list
      const fetchProducts = async () => {
        try {
          const { products, count } = await getProducts(params);
          setProducts(products);
          setTotalCount(count);
        } catch (error) {
          console.error('Error refreshing products:', error);
        }
      };
      fetchProducts();
    }
    setShowCSVUpload(false);
  };

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Products"
        description="Manage marketplace products"
        actionHref="/admin/products/new"
        actionLabel="Add Product"
      />

      {/* CSV Upload Section */}
      {showCSVUpload && (
        <CSVUpload
          type="products"
          onUploadComplete={handleCSVUploadComplete}
        />
      )}

      {/* Action Buttons */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          onClick={() => setShowCSVUpload(!showCSVUpload)}
        >
          <Upload className="h-4 w-4 mr-2" />
          {showCSVUpload ? 'Hide' : 'Show'} CSV Upload
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-4">
        <div className="w-full sm:w-auto">
          <Select
            value={params.featured === undefined ? 'all' : String(params.featured)}
            onValueChange={handleFeaturedFilter}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Featured" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Featured</SelectItem>
              <SelectItem value="true">Featured</SelectItem>
              <SelectItem value="false">Not Featured</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="w-full sm:w-auto">
          <Select
            value={params.trending === undefined ? 'all' : String(params.trending)}
            onValueChange={handleTrendingFilter}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Trending" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Trending</SelectItem>
              <SelectItem value="true">Trending</SelectItem>
              <SelectItem value="false">Not Trending</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="w-full sm:w-auto">
          <Select
            value={params.in_stock === undefined ? 'all' : String(params.in_stock)}
            onValueChange={handleInStockFilter}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Stock Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Stock</SelectItem>
              <SelectItem value="true">In Stock</SelectItem>
              <SelectItem value="false">Out of Stock</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <DataTable
        columns={columns}
        data={products}
        totalCount={totalCount}
        pageSize={params.per_page || 10}
        currentPage={params.page || 1}
        onPageChange={handlePageChange}
        onSearch={handleSearch}
        searchPlaceholder="Search products..."
        onSortChange={handleSortChange}
        sortKey={params.sort_by}
        sortOrder={params.sort_order}
        isLoading={loading}
        getRowKey={(product) => product.id}
      />
    </div>
  );
}
