"use client";
import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { AdminHeader, StatusBadge } from '@/components/admin';
import { createSPASassClient } from '@/lib/supabase/client';
import { NotificationService } from '@/lib/services/notification';
import { getOrder, updateOrder, getPaymentsByOrderId, updatePayment, startOrderReview, confirmPayment } from '@/features/admin/api';
import { ORDER_STATUS } from '@/features/admin/types';
import { AdminOrder, AdminPayment, UpdateOrderParams } from '@/features/admin/types';
import { Button } from '@/components/ui/button';
import { formatCurrency } from '@/lib/utils';
import Link from 'next/link';
import Image from 'next/image';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger
} from '@/components/ui/tabs';
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  CreditCard,
  Edit,
  MapPin,
  Package,
  Phone,
  ShoppingBag,
  Truck,
  User,
  Mail
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import OrderStatusIndicator from '@/components/checkout/OrderStatusIndicator';

interface OrderItem {
  id: string;
  product_id: string;
  store_id: string;
  quantity: number;
  price: number;
  total: number;
  product?: {
    name: string;
    images?: { url: string }[];
  };
}

interface ExtendedAdminOrder extends AdminOrder {
  shipping_name?: string;
  shipping_email?: string;
  shipping_phone?: string;
  shipping_address?: string;
  shipping_city?: string;
  shipping_state?: string;
  shipping_country?: string;
  shipping_postal_code?: string;
  notes?: string;
  items?: OrderItem[];
}

export default function OrderDetailsPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const [order, setOrder] = useState<ExtendedAdminOrder | null>(null);
  const [payments, setPayments] = useState<AdminPayment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState(false);
  const [confirmingPayment, setConfirmingPayment] = useState(false);
  const [orderId, setOrderId] = useState<string | null>(null);

  // Unwrap params using React.use()
  useEffect(() => {
    const unwrapParams = async () => {
      const resolvedParams = await params;
      setOrderId(resolvedParams.id);
    };
    unwrapParams();
  }, [params]);

  useEffect(() => {
    if (!orderId) return;

    const fetchOrder = async () => {
      setLoading(true);
      try {
        // Use the getOrder function from the admin API
        const orderData = await getOrder(orderId);

        if (!orderData) {
          setError('Order not found');
          setLoading(false);
          return;
        }

        // Set the order data
        setOrder(orderData as ExtendedAdminOrder);

        // Fetch payment information
        const paymentData = await getPaymentsByOrderId(orderId);
        setPayments(paymentData);
      } catch (error) {
        console.error('Error fetching order:', error);
        setError('Failed to load order details');
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();
  }, [orderId]);

  const handleConfirmOrder = async () => {
    if (!order) return;

    setUpdating(true);
    try {
      // Determine the next status based on current status
      let nextStatus = 'processing';
      if (order.status === 'processing') {
        nextStatus = 'shipped';
      } else if (order.status === 'shipped') {
        nextStatus = 'delivered';
      }

      // Use the updateOrder function from the admin API
      const updatedOrder = await updateOrder(order.id, { status: nextStatus });

      if (!updatedOrder) {
        setError('Failed to update order status');
        return;
      }

      // Refresh the order data using the getOrder function
      const refreshedOrder = await getOrder(order.id);

      if (refreshedOrder) {
        setOrder(refreshedOrder as ExtendedAdminOrder);
      }
    } catch (error) {
      console.error('Error updating order:', error);
      setError('An error occurred while updating the order');
    } finally {
      setUpdating(false);
    }
  };

  const handleConfirmPayment = async () => {
    if (!payments.length || !orderId) return;

    setConfirmingPayment(true);
    try {
      // Find the pending payment
      const pendingPayment = payments.find(p => p.payment_status === 'pending');

      if (!pendingPayment) {
        setError('No pending payment found');
        return;
      }

      // Update payment status to completed
      const updatedPayment = await updatePayment(pendingPayment.id, {
        payment_status: 'completed'
      });

      if (!updatedPayment) {
        setError('Failed to confirm payment');
        return;
      }

      // Update order status to processing if it's still pending
      if (order && order.status === 'pending') {
        await updateOrder(order.id, { status: 'processing' });
      }

      // Refresh payment data
      const refreshedPayments = await getPaymentsByOrderId(orderId as string);
      setPayments(refreshedPayments);

      // Refresh order data
      const refreshedOrder = await getOrder(orderId as string);
      if (refreshedOrder) {
        setOrder(refreshedOrder as ExtendedAdminOrder);
      }
    } catch (error) {
      console.error('Error confirming payment:', error);
      setError('An error occurred while confirming the payment');
    } finally {
      setConfirmingPayment(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="space-y-6">
        <AdminHeader
          title="Order Not Found"
          description="The requested order could not be found"
          backHref="/admin/orders"
        />
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            The order you are trying to view does not exist or you don&apos;t have permission to view it.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Handle order review workflow
  const handleStartReview = async () => {
    setUpdating(true);
    setError(null);

    try {
      const success = await startOrderReview(order.id);
      if (!success) {
        setError('Failed to start order review');
        return;
      }

      // Refresh the order data
      const refreshedOrder = await getOrder(order.id);
      if (refreshedOrder) {
        setOrder(refreshedOrder as ExtendedAdminOrder);
      }
    } catch (error) {
      console.error('Error starting order review:', error);
      setError('An error occurred while starting order review');
    } finally {
      setUpdating(false);
    }
  };

  // Handle payment confirmation workflow
  const handleConfirmOrderPayment = async () => {
    setUpdating(true);
    setError(null);

    try {
      const success = await confirmPayment(order.id);
      if (!success) {
        setError('Failed to confirm payment');
        return;
      }

      // Refresh the order data
      const refreshedOrder = await getOrder(order.id);
      if (refreshedOrder) {
        setOrder(refreshedOrder as ExtendedAdminOrder);
      }
    } catch (error) {
      console.error('Error confirming payment:', error);
      setError('An error occurred while confirming payment');
    } finally {
      setUpdating(false);
    }
  };

  // Determine the workflow buttons to show
  const getWorkflowButtons = () => {
    switch (order.status) {
      case ORDER_STATUS.PENDING:
        return (
          <Button
            onClick={handleStartReview}
            disabled={updating}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {updating ? 'Starting Review...' : 'Start Review'}
          </Button>
        );

      case ORDER_STATUS.ACCEPTED_BY_STORE:
        return (
          <Button
            onClick={handleConfirmOrderPayment}
            disabled={updating}
            className="bg-green-600 hover:bg-green-700"
          >
            {updating ? 'Confirming Payment...' : 'Confirm Payment'}
          </Button>
        );

      case ORDER_STATUS.PROCESSING:
        return (
          <Button
            onClick={handleConfirmOrder}
            disabled={updating}
            className="bg-primary-600 hover:bg-primary-700"
          >
            {updating ? 'Updating...' : 'Mark as Shipped'}
          </Button>
        );

      case ORDER_STATUS.SHIPPED:
        return (
          <Button
            onClick={handleConfirmOrder}
            disabled={updating}
            className="bg-primary-600 hover:bg-primary-700"
          >
            {updating ? 'Updating...' : 'Mark as Delivered'}
          </Button>
        );

      default:
        return null;
    }
  };

  // Determine if any workflow action is available
  const hasWorkflowAction = ['pending', 'accepted_by_store', 'processing', 'shipped'].includes(order.status);

  return (
    <div className="space-y-6">
      <AdminHeader
        title={`Order #${order.id.substring(0, 8)}`}
        description="View and manage order details"
        backHref="/admin/orders"
      />

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Order Status */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <CardTitle>Order Status</CardTitle>
            <StatusBadge status={order.status} size="lg" />
          </div>
        </CardHeader>
        <CardContent>
          <OrderStatusIndicator status={order.status} />
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="flex gap-2">
            {getWorkflowButtons()}
          </div>
          <Link href={`/admin/orders/${order.id}/edit`}>
            <Button variant="outline">
              <Edit className="mr-2 h-4 w-4" />
              Edit Order
            </Button>
          </Link>
        </CardFooter>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Customer Information */}
        <Card className="md:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="mr-2 h-5 w-5" />
              Customer Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="text-sm font-medium text-gray-500">Name</div>
              <div>{order.shipping_name || 'N/A'}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500">Email</div>
              <div className="flex items-center">
                <Mail className="mr-2 h-4 w-4 text-gray-400" />
                <span>{order.shipping_email || order.user_email || 'N/A'}</span>
              </div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500">Phone</div>
              <div className="flex items-center">
                <Phone className="mr-2 h-4 w-4 text-gray-400" />
                <span>{order.shipping_phone || 'N/A'}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Shipping Information */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center">
              <MapPin className="mr-2 h-5 w-5" />
              Shipping Address
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p>{order.shipping_name || 'N/A'}</p>
              <p>{order.shipping_address || 'N/A'}</p>
              <p>
                {[
                  order.shipping_city,
                  order.shipping_state,
                  order.shipping_postal_code,
                  order.shipping_country
                ]
                  .filter(Boolean)
                  .join(', ') || 'N/A'}
              </p>
              <p>{order.shipping_phone || 'N/A'}</p>
            </div>
          </CardContent>
        </Card>
      </div>

         {/* Payment Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="mr-2 h-5 w-5" />
            Payment Information
          </CardTitle>
          <CardDescription>
            Payment status and transaction details
          </CardDescription>
        </CardHeader>
        <CardContent>
          {payments.length > 0 ? (
            <div className="space-y-4">
              {payments.map((payment, index) => (
                <div key={payment.id} className="border rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <div className="text-sm font-medium text-gray-500">Payment #{index + 1}</div>
                      <div className="text-lg font-medium">{formatCurrency(payment.amount, payment.currency)}</div>
                    </div>
                    <div className="text-right">
                      <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        payment.payment_status === 'completed'
                          ? 'bg-green-100 text-green-800'
                          : payment.payment_status === 'pending'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {payment.payment_status === 'completed' ? 'Paid' :
                         payment.payment_status === 'pending' ? 'Pending' : 'Failed'}
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-gray-500">Payment Method</div>
                      <div>{payment.payment_method}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Transaction ID</div>
                      <div>{payment.transaction_id || 'N/A'}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Payment Date</div>
                      <div>{new Date(payment.created_at).toLocaleString()}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Last Updated</div>
                      <div>{new Date(payment.updated_at).toLocaleString()}</div>
                    </div>
                  </div>

                  {payment.payment_status === 'pending' && (
                    <div className="mt-4 pt-4 border-t">
                      <Button
                        onClick={handleConfirmPayment}
                        disabled={confirmingPayment}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        {confirmingPayment ? 'Confirming...' : 'Confirm Payment Received'}
                      </Button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4 text-gray-500">
              No payment information available for this order
            </div>
          )}
        </CardContent>
      </Card>

      {/* Order Items */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ShoppingBag className="mr-2 h-5 w-5" />
            Order Items
          </CardTitle>
          <CardDescription>
            {order.items_count || order.items?.length || 0} items in this order
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {order.items && order.items.length > 0 ? (
              order.items.map((item) => (
                <div key={item.id} className="flex items-start border-b pb-4">
                  <div className="h-16 w-16 bg-gray-100 rounded overflow-hidden relative flex-shrink-0">
                    {item.product?.images && item.product.images.length > 0 ? (
                      <Image
                        src={item.product.images[0].url}
                        alt={item.product.name || 'Product image'}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full w-full text-gray-400">
                        <Package className="h-8 w-8" />
                      </div>
                    )}
                  </div>
                  <div className="ml-4 flex-1">
                    <div className="flex justify-between">
                      <h4 className="font-medium">{item.product?.name || `Product ID: ${item.product_id}`}</h4>
                      <span className="font-medium">{formatCurrency(item.total, order.currency)}</span>
                    </div>
                    <div className="flex justify-between text-sm text-gray-500 mt-1">
                      <span>Qty: {item.quantity} × {formatCurrency(item.price, order.currency)}</span>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-4 text-gray-500">No items found in this order</div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Order Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-500">Subtotal</span>
              <span>{formatCurrency(order.total, order.currency)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Shipping</span>
              <span>Free</span>
            </div>
            <Separator className="my-2" />
            <div className="flex justify-between font-medium">
              <span>Total</span>
              <span>{formatCurrency(order.total, order.currency)}</span>
            </div>
          </div>
        </CardContent>
      </Card>



      {/* Additional Information */}
      <Card>
        <CardHeader>
          <CardTitle>Additional Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <div className="text-sm font-medium text-gray-500">Order Date</div>
            <div className="flex items-center">
              <Calendar className="mr-2 h-4 w-4 text-gray-400" />
              <span>{new Date(order.created_at).toLocaleString()}</span>
            </div>
          </div>
          {order.updated_at && (
            <div>
              <div className="text-sm font-medium text-gray-500">Last Updated</div>
              <div className="flex items-center">
                <Clock className="mr-2 h-4 w-4 text-gray-400" />
                <span>{new Date(order.updated_at).toLocaleString()}</span>
              </div>
            </div>
          )}
          {order.notes && (
            <div>
              <div className="text-sm font-medium text-gray-500">Order Notes</div>
              <div className="p-3 bg-gray-50 rounded-md mt-1">{order.notes}</div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
