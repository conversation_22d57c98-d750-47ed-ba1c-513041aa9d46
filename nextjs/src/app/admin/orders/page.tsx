"use client";
import React, { useState, useEffect } from 'react';
import { AdminHeader, DataTable, StatusBadge } from '@/components/admin';
import { getOrders, getPaymentsByOrderId, getCurrentUserRole } from '@/features/admin/api';
import { AdminOrder, OrderListParams } from '@/features/admin/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Edit, Eye, Package, CreditCard } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { formatCurrency } from '@/lib/utils/format';

export default function OrdersPage() {
  console.log('OrdersPage: Component rendering');

  const [orders, setOrders] = useState<AdminOrder[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [paymentStatusFilter, setPaymentStatusFilter] = useState<string>('all');
  const [userRole, setUserRole] = useState<string | null>(null);
  const [params, setParams] = useState<OrderListParams>({
    page: 1,
    per_page: 10,
    sort_by: 'created_at',
    sort_order: 'desc',
  });

  // Get user role on component mount
  useEffect(() => {
    const fetchUserRole = async () => {
      try {
        const role = await getCurrentUserRole();
        setUserRole(role);
      } catch (error) {
        console.error('Error fetching user role:', error);
      }
    };

    fetchUserRole();
  }, []);

  useEffect(() => {
    const fetchOrders = async () => {
      setLoading(true);
      try {
        const { orders, count } = await getOrders(params);

        let ordersWithPayments;

        // Only fetch payment information for admins, not store owners
        if (userRole === 'admin') {
          console.log('OrdersPage: User is admin, fetching payment information');
          ordersWithPayments = await Promise.all(
            orders.map(async (order) => {
              const payments = await getPaymentsByOrderId(order.id);
              const latestPayment = payments[0]; // Get the most recent payment
              return {
                ...order,
                payments,
                payment_status: latestPayment?.payment_status || 'no_payment',
                payment_method: latestPayment?.payment_method || '',
                transaction_id: latestPayment?.transaction_id || '',
              };
            })
          );
        } else {
          console.log('OrdersPage: User is store owner, skipping payment information');
          // For store owners, just add default payment fields
          ordersWithPayments = orders.map(order => ({
            ...order,
            payments: [],
            payment_status: 'no_payment',
            payment_method: '',
            transaction_id: '',
          }));
        }

        // Filter by payment status if needed
        const filteredOrders = paymentStatusFilter === 'all'
          ? ordersWithPayments
          : ordersWithPayments.filter(order => order.payment_status === paymentStatusFilter);

        setOrders(filteredOrders);
        setTotalCount(count);
      } catch (error) {
        console.error('Error fetching orders:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, [params, paymentStatusFilter, userRole]);

  const handlePageChange = (page: number) => {
    setParams({ ...params, page });
  };

  const handleSearch = (search: string) => {
    setParams({ ...params, page: 1, search });
  };

  const handleSortChange = (sort_by: string, sort_order: 'asc' | 'desc') => {
    setParams({ ...params, sort_by, sort_order });
  };

  const handleStatusFilter = (status: string) => {
    setParams({
      ...params,
      page: 1,
      status: status === 'all' ? undefined : status,
    });
  };

  const handlePaymentStatusFilter = (status: string) => {
    setPaymentStatusFilter(status);
  };

  // Payment status badge component
  const PaymentStatusBadge = ({ status }: { status: string }) => {
    const getStatusColor = (status: string) => {
      switch (status) {
        case 'completed':
          return 'bg-green-100 text-green-800';
        case 'pending':
          return 'bg-yellow-100 text-yellow-800';
        case 'failed':
          return 'bg-red-100 text-red-800';
        case 'no_payment':
          return 'bg-gray-100 text-gray-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    };

    const getStatusText = (status: string) => {
      switch (status) {
        case 'completed':
          return 'Paid';
        case 'pending':
          return 'Pending';
        case 'failed':
          return 'Failed';
        case 'no_payment':
          return 'No Payment';
        default:
          return status;
      }
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(status)}`}>
        {getStatusText(status)}
      </span>
    );
  };

  // Define base columns that all users can see
  const baseColumns = [
    {
      key: 'id',
      header: 'Order ID',
      cell: (order: AdminOrder) => (
        <div className="font-medium text-primary-600">
          {order.id.substring(0, 8)}...
        </div>
      ),
      sortable: true,
    },
    {
      key: 'user_email',
      header: 'Customer',
      cell: (order: AdminOrder) => (
        <div>{order.user_email || order.user_id.substring(0, 8)}</div>
      ),
      sortable: true,
    },
    {
      key: 'total',
      header: 'Total',
      cell: (order: AdminOrder) => (
        <div className="font-medium">
          {formatCurrency(order.total, order.currency)}
        </div>
      ),
      sortable: true,
    },
    {
      key: 'status',
      header: 'Order Status',
      cell: (order: AdminOrder) => <StatusBadge status={order.status} />,
      sortable: true,
    },
    {
      key: 'items_count',
      header: 'Items',
      cell: (order: AdminOrder) => (
        <div className="flex items-center gap-1">
          <Package className="h-4 w-4 text-gray-400" />
          <span>{order.items_count || 0}</span>
        </div>
      ),
    },
    {
      key: 'created_at',
      header: 'Date',
      cell: (order: AdminOrder) => (
        <div>{new Date(order.created_at).toLocaleDateString()}</div>
      ),
      sortable: true,
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (order: AdminOrder) => (
        <div className="flex items-center gap-2">
          <Link href={`/admin/orders/${order.id}`}>
            <Button variant="ghost" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <Link href={`/admin/orders/${order.id}/edit`}>
            <Button variant="ghost" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      ),
    },
  ];

  // Payment columns only for admins
  const paymentColumns = [
    {
      key: 'payment_status',
      header: 'Payment Status',
      cell: (order: AdminOrder) => <PaymentStatusBadge status={order.payment_status || 'no_payment'} />,
      sortable: true,
    },
    {
      key: 'payment_method',
      header: 'Payment Method',
      cell: (order: AdminOrder) => (
        <div className="flex items-center gap-1">
          <CreditCard className="h-4 w-4 text-gray-400" />
          <span>{order.payment_method || '-'}</span>
        </div>
      ),
    },
  ];

  // Combine columns based on user role
  const columns = userRole === 'admin'
    ? [
        ...baseColumns.slice(0, 4), // id, customer, total, status
        ...paymentColumns, // payment status, payment method
        ...baseColumns.slice(4) // items, date, actions
      ]
    : baseColumns;

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Orders"
        description="Manage orders and payments"
      />

      {/* Filters */}
      <div className="flex flex-wrap gap-4">
        <div className="w-full sm:w-auto">
          <Select
            value={params.status || 'all'}
            onValueChange={handleStatusFilter}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Order Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Order Statuses</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="processing">Processing</SelectItem>
              <SelectItem value="shipped">Shipped</SelectItem>
              <SelectItem value="delivered">Delivered</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
              <SelectItem value="refunded">Refunded</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {/* Payment status filter only for admins */}
        {userRole === 'admin' && (
          <div className="w-full sm:w-auto">
            <Select
              value={paymentStatusFilter}
              onValueChange={handlePaymentStatusFilter}
            >
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Payment Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Payment Statuses</SelectItem>
                <SelectItem value="pending">Pending Payment</SelectItem>
                <SelectItem value="completed">Paid</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
                <SelectItem value="no_payment">No Payment</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
      </div>

      <DataTable
        columns={columns}
        data={orders}
        totalCount={totalCount}
        pageSize={params.per_page || 10}
        currentPage={params.page || 1}
        onPageChange={handlePageChange}
        onSearch={handleSearch}
        searchPlaceholder="Search orders..."
        onSortChange={handleSortChange}
        sortKey={params.sort_by}
        sortOrder={params.sort_order}
        isLoading={loading}
        getRowKey={(order) => order.id}
      />
    </div>
  );
}
