'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Plus, Search, Filter, Edit, Trash2, Eye } from 'lucide-react';
import { AdminHeader, DataTable, StatusBadge } from '@/components/admin';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { getDeals, deleteDeal } from '@/features/admin/api';
import { AdminDeal, DealListParams, DealType } from '@/features/admin/types';
import { formatCurrency } from '@/lib/utils';
import { useToast } from '@/lib/hooks/use-toast';

export default function AdminDealsPage() {
  const [deals, setDeals] = useState<AdminDeal[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [dealTypeFilter, setDealTypeFilter] = useState<DealType | 'all'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [perPage] = useState(10);
  const { toast } = useToast();

  const fetchDeals = async () => {
    try {
      setLoading(true);
      const params: DealListParams = {
        page: currentPage,
        per_page: perPage,
        search: searchTerm,
        deal_type: dealTypeFilter === 'all' ? undefined : dealTypeFilter,
        is_active: statusFilter === 'all' ? undefined : statusFilter === 'active',
        sort_by: 'created_at',
        sort_order: 'desc'
      };

      const { deals: fetchedDeals, count } = await getDeals(params);
      setDeals(fetchedDeals);
      setTotalCount(count);
    } catch (error) {
      console.error('Error fetching deals:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch deals',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDeals();
  }, [currentPage, searchTerm, dealTypeFilter, statusFilter]);

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this deal?')) return;

    try {
      const success = await deleteDeal(id);
      if (success) {
        toast({
          title: 'Success',
          description: 'Deal deleted successfully'
        });

        // Force refresh the deals list with a small delay to ensure database consistency
        setTimeout(() => {
          fetchDeals();
        }, 500);

        // Also immediately remove the deal from the local state for instant UI feedback
        setDeals(prevDeals => prevDeals.filter(deal => deal.id !== id));
        setTotalCount(prev => Math.max(0, prev - 1));
      } else {
        throw new Error('Failed to delete deal');
      }
    } catch (error) {
      console.error('Error deleting deal:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete deal',
        variant: 'destructive'
      });
    }
  };

  const getDealTypeColor = (type: DealType) => {
    switch (type) {
      case 'flash': return 'bg-red-100 text-red-800';
      case 'clearance': return 'bg-orange-100 text-orange-800';
      case 'seasonal': return 'bg-blue-100 text-blue-800';
      case 'regular': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const isExpired = (endDate: string) => {
    return new Date(endDate) < new Date();
  };

  const columns = [
    {
      key: 'title',
      header: 'Deal Title',
      cell: (deal: AdminDeal) => (
        <div>
          <div className="font-medium">{deal.title}</div>
          <div className="text-sm text-gray-500">{deal.product_name}</div>
          <div className="text-xs text-gray-400">{deal.store_name}</div>
        </div>
      )
    },
    {
      key: 'prices',
      header: 'Pricing',
      cell: (deal: AdminDeal) => (
        <div>
          <div className="text-sm">
            <span className="line-through text-gray-500">
              {formatCurrency(deal.original_price, deal.currency)}
            </span>
            {' → '}
            <span className="font-medium text-primary-600">
              {formatCurrency(deal.deal_price, deal.currency)}
            </span>
          </div>
          <div className="text-xs text-green-600 font-medium">
            {deal.discount_percentage}% OFF
          </div>
        </div>
      )
    },
    {
      key: 'deal_type',
      header: 'Type',
      cell: (deal: AdminDeal) => (
        <Badge className={getDealTypeColor(deal.deal_type)}>
          {deal.deal_type.charAt(0).toUpperCase() + deal.deal_type.slice(1)}
        </Badge>
      )
    },
    {
      key: 'duration',
      header: 'Duration',
      cell: (deal: AdminDeal) => (
        <div className="text-sm">
          <div>{formatDate(deal.start_date)}</div>
          <div className={isExpired(deal.end_date) ? 'text-red-600' : 'text-gray-600'}>
            {formatDate(deal.end_date)}
          </div>
          {isExpired(deal.end_date) && (
            <div className="text-xs text-red-500">Expired</div>
          )}
        </div>
      )
    },
    {
      key: 'status',
      header: 'Status',
      cell: (deal: AdminDeal) => (
        <div>
          <StatusBadge
            status={deal.is_active ? 'active' : 'inactive'}
            variant={deal.is_active ? 'success' : 'secondary'}
          />
          {deal.featured && (
            <Badge className="ml-1 bg-yellow-100 text-yellow-800">Featured</Badge>
          )}
        </div>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (deal: AdminDeal) => (
        <div className="flex space-x-2">
          <Link href={`/admin/deals/${deal.id}`}>
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <Link href={`/admin/deals/${deal.id}/edit`}>
            <Button variant="outline" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDelete(deal.id)}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Deals"
        description="Manage marketplace deals and promotions"
        actionHref="/admin/deals/new"
        actionLabel="Create Deal"
        actionIcon={Plus}
      />

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search deals..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Deal Type
            </label>
            <select
              value={dealTypeFilter}
              onChange={(e) => setDealTypeFilter(e.target.value as DealType | 'all')}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">All Types</option>
              <option value="flash">Flash</option>
              <option value="clearance">Clearance</option>
              <option value="seasonal">Seasonal</option>
              <option value="regular">Regular</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <div className="flex items-end">
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm('');
                setDealTypeFilter('all');
                setStatusFilter('all');
              }}
              className="w-full"
            >
              <Filter className="h-4 w-4 mr-2" />
              Clear Filters
            </Button>
          </div>
        </div>
      </div>

      {/* Deals Table */}
      <div className="bg-white rounded-lg shadow-sm border">
        <DataTable
          columns={columns}
          data={deals}
          totalCount={totalCount}
          pageSize={perPage}
          currentPage={currentPage}
          onPageChange={setCurrentPage}
          onSearch={(search) => setSearchTerm(search)}
          searchPlaceholder="Search deals..."
          isLoading={loading}
          getRowKey={(deal) => deal.id}
        />
      </div>
    </div>
  );
}
