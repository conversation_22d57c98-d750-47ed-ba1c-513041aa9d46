'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, Calendar, DollarSign } from 'lucide-react';
import Link from 'next/link';
import { AdminHeader } from '@/components/admin';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { createDeal } from '@/features/admin/api';
import { getProducts } from '@/features/admin/api';
import { CreateDealParams, DealType, AdminProduct } from '@/features/admin/types';
import { useToast } from '@/lib/hooks/use-toast';

export default function CreateDealPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<AdminProduct[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProduct, setSelectedProduct] = useState<AdminProduct | null>(null);
  const [showProductSearch, setShowProductSearch] = useState(false);

  const [formData, setFormData] = useState<CreateDealParams>({
    product_id: '',
    title: '',
    description: '',
    original_price: 0,
    deal_price: 0,
    deal_type: 'regular',
    start_date: new Date().toISOString().split('T')[0],
    end_date: '',
    is_active: true,
    featured: false,
    max_quantity: undefined,
    currency: 'GMD'
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch products for selection
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const { products: fetchedProducts } = await getProducts({
          search: searchTerm,
          per_page: 20
        });
        setProducts(fetchedProducts);
      } catch (error) {
        console.error('Error fetching products:', error);
      }
    };

    if (showProductSearch) {
      fetchProducts();
    }
  }, [searchTerm, showProductSearch]);

  // Auto-fill original price when product is selected
  useEffect(() => {
    if (selectedProduct) {
      setFormData(prev => ({
        ...prev,
        product_id: selectedProduct.id,
        original_price: selectedProduct.price,
        title: `${selectedProduct.name} - Special Deal`,
        currency: selectedProduct.currency
      }));
    }
  }, [selectedProduct]);

  const handleInputChange = (field: keyof CreateDealParams, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.product_id) newErrors.product_id = 'Product is required';
    if (!formData.title.trim()) newErrors.title = 'Title is required';
    if (formData.original_price <= 0) newErrors.original_price = 'Original price must be greater than 0';
    if (formData.deal_price <= 0) newErrors.deal_price = 'Deal price must be greater than 0';
    if (formData.deal_price >= formData.original_price) {
      newErrors.deal_price = 'Deal price must be less than original price';
    }
    if (!formData.start_date) newErrors.start_date = 'Start date is required';
    if (!formData.end_date) newErrors.end_date = 'End date is required';
    if (new Date(formData.end_date) <= new Date(formData.start_date)) {
      newErrors.end_date = 'End date must be after start date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      setLoading(true);
      const deal = await createDeal(formData);
      
      if (deal) {
        toast({
          title: 'Success',
          description: 'Deal created successfully'
        });
        router.push('/admin/deals');
      } else {
        throw new Error('Failed to create deal');
      }
    } catch (error) {
      console.error('Error creating deal:', error);
      toast({
        title: 'Error',
        description: 'Failed to create deal',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const discountPercentage = formData.original_price > 0 && formData.deal_price > 0 
    ? Math.round(((formData.original_price - formData.deal_price) / formData.original_price) * 100)
    : 0;

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Create Deal"
        description="Create a new deal for a product"
        breadcrumbs={[
          { label: 'Deals', href: '/admin/deals' },
          { label: 'Create Deal' }
        ]}
      />

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-medium mb-4">Deal Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Product Selection */}
            <div className="md:col-span-2">
              <Label htmlFor="product">Product *</Label>
              {selectedProduct ? (
                <div className="mt-1 p-3 border rounded-md bg-gray-50">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-medium">{selectedProduct.name}</div>
                      <div className="text-sm text-gray-500">{selectedProduct.store_name}</div>
                      <div className="text-sm text-gray-500">
                        Current Price: {selectedProduct.currency} {selectedProduct.price}
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedProduct(null);
                        setShowProductSearch(true);
                      }}
                    >
                      Change
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="mt-1">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowProductSearch(true)}
                    className="w-full"
                  >
                    Select Product
                  </Button>
                </div>
              )}
              {errors.product_id && (
                <p className="text-red-500 text-sm mt-1">{errors.product_id}</p>
              )}
            </div>

            {/* Title */}
            <div className="md:col-span-2">
              <Label htmlFor="title">Deal Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Enter deal title"
                className="mt-1"
              />
              {errors.title && (
                <p className="text-red-500 text-sm mt-1">{errors.title}</p>
              )}
            </div>

            {/* Description */}
            <div className="md:col-span-2">
              <Label htmlFor="description">Description</Label>
              <textarea
                id="description"
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Enter deal description"
                rows={3}
                className="mt-1 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            {/* Pricing */}
            <div>
              <Label htmlFor="original_price">Original Price *</Label>
              <div className="mt-1 relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  id="original_price"
                  type="number"
                  step="0.01"
                  value={formData.original_price}
                  onChange={(e) => handleInputChange('original_price', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  className="pl-10"
                />
              </div>
              {errors.original_price && (
                <p className="text-red-500 text-sm mt-1">{errors.original_price}</p>
              )}
            </div>

            <div>
              <Label htmlFor="deal_price">Deal Price *</Label>
              <div className="mt-1 relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  id="deal_price"
                  type="number"
                  step="0.01"
                  value={formData.deal_price}
                  onChange={(e) => handleInputChange('deal_price', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  className="pl-10"
                />
              </div>
              {discountPercentage > 0 && (
                <p className="text-green-600 text-sm mt-1">
                  {discountPercentage}% discount
                </p>
              )}
              {errors.deal_price && (
                <p className="text-red-500 text-sm mt-1">{errors.deal_price}</p>
              )}
            </div>

            {/* Deal Type */}
            <div>
              <Label htmlFor="deal_type">Deal Type</Label>
              <select
                id="deal_type"
                value={formData.deal_type}
                onChange={(e) => handleInputChange('deal_type', e.target.value as DealType)}
                className="mt-1 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="regular">Regular</option>
                <option value="flash">Flash Sale</option>
                <option value="clearance">Clearance</option>
                <option value="seasonal">Seasonal</option>
              </select>
            </div>

            {/* Max Quantity */}
            <div>
              <Label htmlFor="max_quantity">Max Quantity (Optional)</Label>
              <Input
                id="max_quantity"
                type="number"
                value={formData.max_quantity || ''}
                onChange={(e) => handleInputChange('max_quantity', e.target.value ? parseInt(e.target.value) : undefined)}
                placeholder="Unlimited"
                className="mt-1"
              />
            </div>

            {/* Start Date */}
            <div>
              <Label htmlFor="start_date">Start Date *</Label>
              <div className="mt-1 relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  id="start_date"
                  type="date"
                  value={formData.start_date}
                  onChange={(e) => handleInputChange('start_date', e.target.value)}
                  className="pl-10"
                />
              </div>
              {errors.start_date && (
                <p className="text-red-500 text-sm mt-1">{errors.start_date}</p>
              )}
            </div>

            {/* End Date */}
            <div>
              <Label htmlFor="end_date">End Date *</Label>
              <div className="mt-1 relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  id="end_date"
                  type="date"
                  value={formData.end_date}
                  onChange={(e) => handleInputChange('end_date', e.target.value)}
                  className="pl-10"
                />
              </div>
              {errors.end_date && (
                <p className="text-red-500 text-sm mt-1">{errors.end_date}</p>
              )}
            </div>

            {/* Status Options */}
            <div className="md:col-span-2">
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    id="is_active"
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => handleInputChange('is_active', e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <Label htmlFor="is_active" className="ml-2">
                    Active Deal
                  </Label>
                </div>

                <div className="flex items-center">
                  <input
                    id="featured"
                    type="checkbox"
                    checked={formData.featured}
                    onChange={(e) => handleInputChange('featured', e.target.checked)}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <Label htmlFor="featured" className="ml-2">
                    Featured Deal
                  </Label>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between">
          <Link href="/admin/deals">
            <Button type="button" variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Cancel
            </Button>
          </Link>
          
          <Button type="submit" disabled={loading}>
            <Save className="h-4 w-4 mr-2" />
            {loading ? 'Creating...' : 'Create Deal'}
          </Button>
        </div>
      </form>

      {/* Product Search Modal */}
      {showProductSearch && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-96 overflow-hidden">
            <h3 className="text-lg font-medium mb-4">Select Product</h3>
            
            <Input
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="mb-4"
            />

            <div className="max-h-64 overflow-y-auto space-y-2">
              {products.map((product) => (
                <div
                  key={product.id}
                  onClick={() => {
                    setSelectedProduct(product);
                    setShowProductSearch(false);
                  }}
                  className="p-3 border rounded-md hover:bg-gray-50 cursor-pointer"
                >
                  <div className="font-medium">{product.name}</div>
                  <div className="text-sm text-gray-500">{product.store_name}</div>
                  <div className="text-sm text-gray-500">
                    {product.currency} {product.price}
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4 flex justify-end">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowProductSearch(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
