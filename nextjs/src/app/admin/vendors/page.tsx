"use client";
import React, { useState, useEffect } from 'react';
import { AdminHeader, DataTable, StatusBadge } from '@/components/admin';
import { Button } from '@/components/ui/button';
import { Edit, Eye } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import Link from 'next/link';

interface Vendor {
  id: string;
  name: string;
  contact_email?: string;
  contact_phone?: string;
  commission_rate: number;
  status: 'active' | 'suspended';
  created_at: string;
  updated_at: string;
}

interface VendorListParams {
  page?: number;
  per_page?: number;
  status?: string;
  search?: string;
}

export default function VendorsPage() {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [params, setParams] = useState<VendorListParams>({
    page: 1,
    per_page: 10,
    status: '',
    search: '',
  });
  const [totalCount, setTotalCount] = useState(0);

  const fetchVendors = async () => {
    try {
      setLoading(true);
      const searchParams = new URLSearchParams();
      
      if (params.page) searchParams.set('page', params.page.toString());
      if (params.per_page) searchParams.set('per_page', params.per_page.toString());
      if (params.status) searchParams.set('status', params.status);
      if (params.search) searchParams.set('search', params.search);

      const response = await fetch(`/api/admin/vendors?${searchParams}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch vendors');
      }

      setVendors(data.vendors || []);
      setTotalCount(data.count || 0);
      setError(null);
    } catch (error) {
      console.error('Error fetching vendors:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
      setVendors([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVendors();
  }, [params]);

  const handleStatusFilter = (status: string) => {
    setParams(prev => ({ ...prev, status: status === 'all' ? '' : status, page: 1 }));
  };

  const handleSearch = (search: string) => {
    setParams(prev => ({ ...prev, search, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setParams(prev => ({ ...prev, page }));
  };

  const columns = [
    {
      key: 'name',
      header: 'Vendor Name',
      cell: (vendor: Vendor) => (
        <div>
          <div className="font-medium text-gray-900">{vendor.name}</div>
          {vendor.contact_email && (
            <div className="text-sm text-gray-500">{vendor.contact_email}</div>
          )}
        </div>
      ),
      sortable: true,
    },
    {
      key: 'contact_phone',
      header: 'Phone',
      cell: (vendor: Vendor) => (
        <div className="text-sm text-gray-900">
          {vendor.contact_phone || '-'}
        </div>
      ),
    },
    {
      key: 'commission_rate',
      header: 'Commission',
      cell: (vendor: Vendor) => (
        <div className="text-sm text-gray-900">
          {vendor.commission_rate}%
        </div>
      ),
      sortable: true,
    },
    {
      key: 'status',
      header: 'Status',
      cell: (vendor: Vendor) => (
        <StatusBadge 
          status={vendor.status} 
          variant={vendor.status === 'active' ? 'success' : 'warning'}
        />
      ),
      sortable: true,
    },
    {
      key: 'created_at',
      header: 'Created',
      cell: (vendor: Vendor) => (
        <div className="text-sm text-gray-500">
          {new Date(vendor.created_at).toLocaleDateString()}
        </div>
      ),
      sortable: true,
    },
    {
      key: 'actions',
      header: 'Actions',
      cell: (vendor: Vendor) => (
        <div className="flex items-center gap-2">
          <Link href={`/admin/vendors/${vendor.id}`}>
            <Button variant="ghost" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <Link href={`/admin/vendors/${vendor.id}/edit`}>
            <Button variant="ghost" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      ),
    },
  ];

  if (error) {
    return (
      <div className="space-y-6">
        <AdminHeader
          title="Vendors"
          description="Manage your marketplace vendors"
        />
        <div className="text-center py-12">
          <p className="text-red-500 mb-4">{error}</p>
          <Button onClick={fetchVendors}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <AdminHeader
        title="Vendors"
        description="Manage your marketplace vendors"
        actionHref="/admin/vendors/new"
        actionLabel="Add Vendor"
      />

      {/* Filters */}
      <div className="flex gap-4">
        <Select value={params.status || 'all'} onValueChange={handleStatusFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="suspended">Suspended</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Data Table */}
      <DataTable
        data={vendors}
        columns={columns}
        loading={loading}
        searchable
        searchPlaceholder="Search vendors..."
        onSearch={handleSearch}
        pagination={{
          page: params.page || 1,
          per_page: params.per_page || 10,
          total: totalCount,
          onPageChange: handlePageChange,
        }}
      />
    </div>
  );
}
