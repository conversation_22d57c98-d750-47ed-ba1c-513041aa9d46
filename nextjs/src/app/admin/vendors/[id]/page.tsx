"use client";
import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { AdminHeader, StatusBadge } from '@/components/admin';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Edit, Trash2, Package, DollarSign, Phone, Mail } from 'lucide-react';
import Link from 'next/link';
import { useToast } from '@/lib/hooks/use-toast';

interface Vendor {
  id: string;
  name: string;
  contact_email?: string;
  contact_phone?: string;
  commission_rate: number;
  status: 'active' | 'suspended';
  notes?: string;
  created_at: string;
  updated_at: string;
}

interface VendorStats {
  total_products: number;
  total_orders: number;
  total_revenue: number;
  commission_earned: number;
}

export default function VendorDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [vendor, setVendor] = useState<Vendor | null>(null);
  const [stats, setStats] = useState<VendorStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const vendorId = params.id as string;

  const fetchVendor = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/vendors/${vendorId}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch vendor');
      }

      setVendor(data);
      setError(null);
    } catch (error) {
      console.error('Error fetching vendor:', error);
      setError(error instanceof Error ? error.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch(`/api/admin/vendors/${vendorId}/stats`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch vendor stats');
      }

      setStats(data);
    } catch (error) {
      console.error('Error fetching vendor stats:', error);
      // Set default stats on error
      setStats({
        total_products: 0,
        total_orders: 0,
        total_revenue: 0,
        commission_earned: 0,
      });
    }
  };

  useEffect(() => {
    if (vendorId) {
      fetchVendor();
      fetchStats();
    }
  }, [vendorId]);

  const handleDelete = async () => {
    if (!vendor || !confirm('Are you sure you want to delete this vendor? This action cannot be undone.')) {
      return;
    }

    try {
      setDeleteLoading(true);
      const response = await fetch(`/api/admin/vendors/${vendorId}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete vendor');
      }

      toast({
        title: "Success",
        description: "Vendor deleted successfully",
      });

      router.push('/admin/vendors');
    } catch (error) {
      console.error('Error deleting vendor:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : 'Failed to delete vendor',
        variant: "destructive",
      });
    } finally {
      setDeleteLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <AdminHeader title="Loading..." description="Fetching vendor details" />
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
        </div>
      </div>
    );
  }

  if (error || !vendor) {
    return (
      <div className="space-y-6">
        <AdminHeader title="Error" description="Failed to load vendor" />
        <Alert variant="destructive">
          <AlertDescription>{error || 'Vendor not found'}</AlertDescription>
        </Alert>
        <Link href="/admin/vendors">
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Vendors
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <AdminHeader
        title={vendor.name}
        description="Vendor details and management"
      />

      <div className="flex items-center gap-4 mb-6">
        <Link href="/admin/vendors">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Vendors
          </Button>
        </Link>
        <Link href={`/admin/vendors/${vendorId}/edit`}>
          <Button size="sm">
            <Edit className="h-4 w-4 mr-2" />
            Edit Vendor
          </Button>
        </Link>
        <Button 
          variant="destructive" 
          size="sm" 
          onClick={handleDelete}
          disabled={deleteLoading}
        >
          <Trash2 className="h-4 w-4 mr-2" />
          {deleteLoading ? 'Deleting...' : 'Delete'}
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Vendor Information */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Vendor Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Name</label>
                  <p className="text-lg font-semibold">{vendor.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <div className="mt-1">
                    <StatusBadge 
                      status={vendor.status} 
                      variant={vendor.status === 'active' ? 'success' : 'warning'}
                    />
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Commission Rate</label>
                  <p className="text-lg font-semibold">{vendor.commission_rate}%</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Created</label>
                  <p className="text-sm text-gray-600">
                    {new Date(vendor.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>

              {vendor.contact_email && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Contact Email</label>
                  <p className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    {vendor.contact_email}
                  </p>
                </div>
              )}

              {vendor.contact_phone && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Contact Phone</label>
                  <p className="flex items-center gap-2">
                    <Phone className="h-4 w-4" />
                    {vendor.contact_phone}
                  </p>
                </div>
              )}

              {vendor.notes && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Notes</label>
                  <p className="text-sm text-gray-600 whitespace-pre-wrap">{vendor.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Statistics */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {stats && (
                <>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4 text-blue-500" />
                      <span className="text-sm">Products</span>
                    </div>
                    <Badge variant="secondary">{stats.total_products}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Total Orders</span>
                    </div>
                    <Badge variant="secondary">{stats.total_orders}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-green-500" />
                      <span className="text-sm">Revenue</span>
                    </div>
                    <Badge variant="secondary">GMD {stats.total_revenue.toFixed(2)}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-purple-500" />
                      <span className="text-sm">Commission</span>
                    </div>
                    <Badge variant="secondary">GMD {stats.commission_earned.toFixed(2)}</Badge>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
