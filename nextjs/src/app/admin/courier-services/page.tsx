'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DataTable, AdminHeader } from '@/components/admin';
import { Badge } from '@/components/ui/badge';
import { Plus, Search, Edit, Eye, Trash2 } from 'lucide-react';

interface CourierService {
  id: string;
  name: string;
  description?: string;
  estimated_delivery_time: string;
  base_cost: number;
  cost_per_kg?: number;
  free_weight_limit?: number;
  tracking_supported: boolean;
  countries: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface CourierServicesResponse {
  data: CourierService[];
  meta: {
    total: number;
    page: number;
    per_page: number;
    total_pages: number;
  };
}

export default function CourierServicesPage() {
  const [courierServices, setCourierServices] = useState<CourierService[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const perPage = 10;

  const fetchCourierServices = async () => {
    setLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        per_page: perPage.toString(),
        ...(searchTerm && { search: searchTerm }),
      });
      const apiUrl = `/api/admin/courier-services?${params}`;

      const response = await fetch(apiUrl);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[AdminCSPage] Error response:', errorText);
        throw new Error(`Failed to fetch courier services: ${response.status} ${response.statusText}`);
      }

      const data: CourierServicesResponse = await response.json();
      console.log('[AdminCSPage] Success response:', data);
      console.log('[AdminCSPage] Setting courier services:', data.data);
      console.log('[AdminCSPage] Data length:', data.data?.length);

      setCourierServices(data.data || []);
      setTotalCount(data.meta?.total || 0);

      console.log('[AdminCSPage] State updated. Current services count:', data.data?.length);
    } catch (error: any) {
      console.error('[AdminCSPage] Error in fetchCourierServices:', error.message, error);
      setError(error.message || 'Failed to load courier services');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this courier service?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/courier-services/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete courier service');
      }

      await fetchCourierServices();
    } catch (error) {
      console.error('Error deleting courier service:', error);
      setError('Failed to delete courier service');
    }
  };

  useEffect(() => {
    fetchCourierServices();
  }, [currentPage, searchTerm]);

  const columns = [
    {
      header: 'Name',
      key: 'name' as keyof CourierService,
      cell: (service: CourierService) => (
        <div>
          <div className="font-medium">{service.name}</div>
          {service.description && (
            <div className="text-sm text-gray-500 mt-1">{service.description}</div>
          )}
        </div>
      ),
    },
    {
      header: 'Base Cost',
      key: 'base_cost' as keyof CourierService,
      cell: (service: CourierService) => (
        <div>
          <div className="font-medium">${service.base_cost.toFixed(2)}</div>
          {service.cost_per_kg && (
            <div className="text-sm text-gray-500">
              +${service.cost_per_kg.toFixed(2)}/kg
              {service.free_weight_limit && ` (after ${service.free_weight_limit}kg)`}
            </div>
          )}
        </div>
      ),
    },
    {
      header: 'Delivery Time',
      key: 'estimated_delivery_time' as keyof CourierService,
      cell: (service: CourierService) => service.estimated_delivery_time,
    },
    {
      header: 'Countries',
      key: 'countries' as keyof CourierService,
      cell: (service: CourierService) => (
        <div className="flex flex-wrap gap-1">
          {service.countries.slice(0, 3).map((country) => (
            <Badge key={country} variant="secondary" className="text-xs">
              {country}
            </Badge>
          ))}
          {service.countries.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{service.countries.length - 3}
            </Badge>
          )}
        </div>
      ),
    },
    {
      header: 'Features',
      key: 'tracking_supported' as keyof CourierService,
      cell: (service: CourierService) => (
        <div className="space-y-1">
          {service.tracking_supported && (
            <Badge variant="secondary" className="text-xs">
              Tracking
            </Badge>
          )}
        </div>
      ),
    },
    {
      header: 'Status',
      key: 'is_active' as keyof CourierService,
      cell: (service: CourierService) => (
        <Badge variant={service.is_active ? 'default' : 'secondary'}>
          {service.is_active ? 'Active' : 'Inactive'}
        </Badge>
      ),
    },
    {
      header: 'Actions',
      key: 'actions' as keyof CourierService,
      cell: (service: CourierService) => (
        <div className="flex space-x-2">
          <Link href={`/admin/courier-services/${service.id}`}>
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <Link href={`/admin/courier-services/${service.id}/edit`}>
            <Button variant="outline" size="sm">
              <Edit className="h-4 w-4" />
            </Button>
          </Link>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDelete(service.id)}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <AdminHeader
        title="International Courier Services"
        description="Manage international shipping courier service providers"
        // icon={Globe} // Removed icon prop as it's not supported
      />

      <div className="bg-white rounded-lg border">
        {/* Header with search and actions */}
        <div className="p-6 border-b">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search courier services..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-full sm:w-80"
              />
            </div>
          </div>
        </div>
        <Link href="/admin/courier-services/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add Courier Service
          </Button>
        </Link>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <DataTable
        data={courierServices}
        columns={columns as any}
        totalCount={totalCount}
        pageSize={perPage}
        currentPage={currentPage}
        onPageChange={setCurrentPage}
        onSearch={(search: string) => setSearchTerm(search)}
        searchPlaceholder="Search courier services..."
        isLoading={loading}
        getRowKey={(service: any) => service.id}
      />
    </div>
  );
}