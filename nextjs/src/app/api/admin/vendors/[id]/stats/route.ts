import { NextRequest, NextResponse } from 'next/server';
import { createServerAdminClient } from '@/lib/supabase/serverAdminClient';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const supabase = await createServerAdminClient();

    // Get vendor products count
    const { count: productCount, error: productError } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('vendor_id', id);

    if (productError) {
      return NextResponse.json({ error: productError.message }, { status: 500 });
    }

    // Get vendor orders count and revenue
    const { data: orderItems, error: orderError } = await supabase
      .from('order_items')
      .select('quantity, price')
      .eq('vendor_id', id);

    if (orderError) {
      return NextResponse.json({ error: orderError.message }, { status: 500 });
    }

    // Calculate statistics
    const totalOrders = orderItems?.length || 0;
    const totalRevenue = orderItems?.reduce((sum, item) => sum + (item.quantity * item.price), 0) || 0;

    // Get vendor commission rate
    const { data: vendor, error: vendorError } = await supabase
      .from('vendors')
      .select('commission_rate')
      .eq('id', id)
      .single();

    if (vendorError) {
      return NextResponse.json({ error: vendorError.message }, { status: 500 });
    }

    const commissionRate = vendor?.commission_rate || 10;
    const commissionEarned = totalRevenue * (commissionRate / 100);

    const stats = {
      total_products: productCount || 0,
      total_orders: totalOrders,
      total_revenue: totalRevenue,
      commission_earned: commissionEarned,
    };

    return NextResponse.json(stats);
  } catch (error) {
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
