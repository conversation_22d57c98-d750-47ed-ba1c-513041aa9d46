import { NextRequest, NextResponse } from 'next/server';
import { createServerAdminClient } from '@/lib/supabase/serverAdminClient';

// GET /api/admin/courier-services/[id] - Get courier service by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerAdminClient();

    const { data, error } = await supabase
      .from('courier_services')
      .select('*')
      .eq('id', params.id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Courier service not found' }, { status: 404 });
      }
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log('[API] Retrieved courier service:', data);

    return NextResponse.json({ courierService: data });
  } catch (error) {
    console.error('[API] Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/courier-services/[id] - Update courier service
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`[API] PUT /api/admin/courier-services/${params.id}`);
    
    const supabase = await createServerAdminClient();

    const body = await req.json();
    console.log('[API] Request body:', body);

    // Validate required fields
    if (!body.name || !body.estimated_delivery_time || !body.countries || body.base_cost === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: name, estimated_delivery_time, countries, base_cost' },
        { status: 400 }
      );
    }

    // Update courier service
    const { data, error } = await supabase
      .from('courier_services')
      .update({
        name: body.name,
        description: body.description || null,
        estimated_delivery_time: body.estimated_delivery_time,
        base_cost: body.base_cost,
        cost_per_kg: body.cost_per_kg || null,
        free_weight_limit: body.free_weight_limit || null,
        tracking_supported: body.tracking_supported || false,
        countries: body.countries || [],
        is_active: body.is_active !== undefined ? body.is_active : true,
        updated_at: new Date().toISOString(),
      })
      .eq('id', params.id)
      .select()
      .single();

    if (error) {
      console.error('[API] Database error:', error);
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Courier service not found' }, { status: 404 });
      }
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log('[API] Updated courier service:', data);

    return NextResponse.json({ courierService: data });
  } catch (error) {
    console.error('[API] Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/courier-services/[id] - Delete courier service
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`[API] DELETE /api/admin/courier-services/${params.id}`);
    
    const supabase = await createServerAdminClient();

    const { error } = await supabase
      .from('courier_services')
      .delete()
      .eq('id', params.id);

    if (error) {
      console.error('[API] Database error:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    console.log(`[API] Deleted courier service: ${params.id}`);

    return NextResponse.json({ message: 'Courier service deleted successfully' });
  } catch (error) {
    console.error('[API] Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}