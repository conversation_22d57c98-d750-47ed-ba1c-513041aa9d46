import { NextRequest, NextResponse } from 'next/server';
import { createSPASassClient } from '@/lib/supabase/client';

export async function POST(request: NextRequest) {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // First, let's try to query the system_settings table directly to see if it exists
    const { data: existingSettings, error: tableCheckError } = await supabase
      .from('system_settings')
      .select('count')
      .limit(1);



    // If we get a "relation does not exist" error, the table doesn't exist
    const tableExists = !tableCheckError || !tableCheckError.message?.includes('does not exist');

    if (!tableExists) {
      // Create the system_settings table
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS system_settings (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          setting_key VARCHAR(255) UNIQUE NOT NULL,
          setting_value JSONB NOT NULL,
          description TEXT,
          category VARCHAR(100) DEFAULT 'general',
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_by UUID,
          updated_by UUID
        );
      `;

      // Since we can't execute raw SQL through Supabase client, let's try a different approach
      // We'll try to insert a record and see if the table exists
      
      return NextResponse.json({
        success: false,
        message: 'system_settings table does not exist. Please create it manually in your database.',
        createTableSQL: createTableSQL
      }, { status: 500 });
    }

    // Now try to insert a test service fee configuration
    const defaultConfig = {
      fee_type: 'tiered',
      tiers: [
        { min_amount: 0, max_amount: 1000, fee: 10.00 },
        { min_amount: 1001, max_amount: null, fee: 25.00 }
      ],
      grace_period_days: 7
    };

    // Try to insert or update the service fee config
    const { data: insertData, error: insertError } = await supabase
      .from('system_settings')
      .upsert({
        setting_key: 'service_fee_config',
        setting_value: defaultConfig,
        category: 'fees',
        description: 'Service fee configuration for transactions',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'setting_key'
      });

    if (insertError) {
      console.error('Error inserting config:', insertError);
      return NextResponse.json({
        success: false,
        message: 'Failed to insert service fee configuration',
        error: insertError.message
      }, { status: 500 });
    }

    // Verify the data was inserted
    const { data: verifyData, error: verifyError } = await supabase
      .from('system_settings')
      .select('*')
      .eq('setting_key', 'service_fee_config')
      .single();

    if (verifyError) {
      console.error('Error verifying data:', verifyError);
      return NextResponse.json({
        success: false,
        message: 'Failed to verify service fee configuration',
        error: verifyError.message
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'System settings table setup completed successfully',
      data: verifyData
    });

  } catch (error) {
    console.error('Error setting up system settings:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const client = await createSPASassClient();
    const supabase = client.getSupabaseClient();

    // Get current system settings
    const { data: settings, error: settingsError } = await supabase
      .from('system_settings')
      .select('*');

    return NextResponse.json({
      success: !settingsError,
      tableExists: !settingsError,
      currentSettings: settings || [],
      error: settingsError?.message
    });

  } catch (error) {
    console.error('Error getting system settings info:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}