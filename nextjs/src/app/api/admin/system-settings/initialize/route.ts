import { NextRequest, NextResponse } from 'next/server';
import { SystemSettingsService } from '@/lib/services/system-settings';

export async function POST(request: NextRequest) {
  try {
    // Initialize service fee configuration
    const initialized = await SystemSettingsService.initializeServiceFeeConfig();
    
    if (initialized) {
      // Get the initialized config to return
      const config = await SystemSettingsService.getServiceFeeConfig();
      
      return NextResponse.json({
        success: true,
        message: 'System settings initialized successfully',
        config
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'Failed to initialize system settings'
      }, { status: 500 });
    }
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get current service fee configuration
    const config = await SystemSettingsService.getServiceFeeConfig();
    
    return NextResponse.json({
      success: true,
      config
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}