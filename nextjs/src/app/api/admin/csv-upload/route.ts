import { NextRequest, NextResponse } from 'next/server';
import { createSPASassClient } from '@/lib/supabase/client';

interface CSVRow {
  [key: string]: string;
}

interface UploadResult {
  success: number;
  failed: number;
  errors: string[];
  data?: any[];
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const type = formData.get('type') as string;

    if (!file || !type) {
      return NextResponse.json(
        { error: 'File and type are required' },
        { status: 400 }
      );
    }

    if (file.type !== 'text/csv') {
      return NextResponse.json(
        { error: 'Only CSV files are allowed' },
        { status: 400 }
      );
    }

    // Parse CSV
    const text = await file.text();
    const lines = text.split('\n').filter(line => line.trim());
    
    if (lines.length < 2) {
      return NextResponse.json(
        { error: 'CSV file must contain at least a header and one data row' },
        { status: 400 }
      );
    }

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const rows: CSVRow[] = lines.slice(1).map(line => {
      const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
      const row: CSVRow = {};
      headers.forEach((header, index) => {
        row[header] = values[index] || '';
      });
      return row;
    });

    // Process based on type
    let result: UploadResult;
    if (type === 'products') {
      result = await processProductsCSV(rows);
    } else if (type === 'stores') {
      result = await processStoresCSV(rows);
    } else {
      return NextResponse.json(
        { error: 'Invalid type. Must be "products" or "stores"' },
        { status: 400 }
      );
    }

    return NextResponse.json(result);
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function processProductsCSV(rows: CSVRow[]): Promise<UploadResult> {
  const client = await createSPASassClient();
  const supabase = client.getSupabaseClient();
  
  let success = 0;
  let failed = 0;
  const errors: string[] = [];

  for (let i = 0; i < rows.length; i++) {
    const row = rows[i];
    const rowNumber = i + 2; // +2 because we skip header and arrays are 0-indexed

    try {
      // Validate required fields
      if (!row.name || !row.price) {
        errors.push(`Row ${rowNumber}: Name and price are required`);
        failed++;
        continue;
      }

      // Generate slug from name
      const slug = row.name.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');

      // Prepare product data
      const productData = {
        name: row.name,
        slug: slug,
        description: row.description || null,
        price: parseFloat(row.price) || 0,
        compare_at_price: row.compare_at_price ? parseFloat(row.compare_at_price) : null,
        currency: row.currency || 'GMD',
        category_id: row.category_id || null,
        store_id: row.store_id || null,
        featured: row.featured === 'true' || row.featured === '1',
        trending: row.trending === 'true' || row.trending === '1',
        in_stock: row.in_stock !== 'false' && row.in_stock !== '0', // Default to true
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Insert product
      const { error } = await supabase
        .from('products')
        .insert(productData);

      if (error) {
        errors.push(`Row ${rowNumber}: ${error.message}`);
        failed++;
      } else {
        success++;
      }
    } catch (error) {
      errors.push(`Row ${rowNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      failed++;
    }
  }

  return { success, failed, errors };
}

async function processStoresCSV(rows: CSVRow[]): Promise<UploadResult> {
  const client = await createSPASassClient();
  const supabase = client.getSupabaseClient();
  
  let success = 0;
  let failed = 0;
  const errors: string[] = [];

  for (let i = 0; i < rows.length; i++) {
    const row = rows[i];
    const rowNumber = i + 2;

    try {
      // Validate required fields
      if (!row.name) {
        errors.push(`Row ${rowNumber}: Name is required`);
        failed++;
        continue;
      }

      // Generate slug from name
      const slug = row.name.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');

      // Get current user as owner (admin creating the store)
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        errors.push(`Row ${rowNumber}: Authentication required`);
        failed++;
        continue;
      }

      // Prepare store data
      const storeData = {
        name: row.name,
        slug: slug,
        description: row.description || null,
        address: row.address || '',
        contact_email: row.contact_email || null,
        contact_phone: row.contact_phone || null,
        owner_id: user.id, // Admin becomes the owner
        status: row.status || 'active',
        featured: row.featured === 'true' || row.featured === '1',
        rating: 0,
        review_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Insert store
      const { error } = await supabase
        .from('stores')
        .insert(storeData);

      if (error) {
        errors.push(`Row ${rowNumber}: ${error.message}`);
        failed++;
      } else {
        success++;
      }
    } catch (error) {
      errors.push(`Row ${rowNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      failed++;
    }
  }

  return { success, failed, errors };
}
