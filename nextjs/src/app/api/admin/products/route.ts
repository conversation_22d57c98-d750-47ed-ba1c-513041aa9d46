import { NextRequest, NextResponse } from 'next/server';
import { createServerAdminClient } from '@/lib/supabase/serverAdminClient';

export async function GET(request: NextRequest) {
  try {
    // Get the query parameters
    const { searchParams } = new URL(request.url);
    const vendorId = searchParams.get('vendor_id');
    const categoryId = searchParams.get('category_id');
    const featured = searchParams.get('featured');
    const trending = searchParams.get('trending');
    const inStock = searchParams.get('in_stock');

    const supabase = await createServerAdminClient();

    // Build query
    let query = supabase.from('products').select(`
      *,
      vendors!products_vendor_id_fkey(id, name, status),
      categories!products_category_id_fkey(id, name)
    `);

    // Apply filters
    if (vendorId) {
      query = query.eq('vendor_id', vendorId);
    }

    if (categoryId) {
      query = query.eq('category_id', categoryId);
    }

    if (featured) {
      query = query.eq('featured', featured === 'true');
    }

    if (trending) {
      query = query.eq('trending', trending === 'true');
    }

    if (inStock) {
      query = query.eq('in_stock', inStock === 'true');
    }

    // Get results
    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Transform data to include vendor and category names
    const products = data.map((product: any) => ({
      ...product,
      vendor_name: product.vendors?.name,
      vendor_status: product.vendors?.status,
      category_name: product.categories?.name,
      vendors: undefined,
      categories: undefined
    }));

    return NextResponse.json({
      products: products || [],
      count: products?.length || 0
    });
  } catch (error) {
    console.error('Error in admin products API:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate the request body
    if (!body || typeof body !== 'object' || !body.name || !body.slug || !body.vendor_id) {
      return NextResponse.json({
        error: 'Invalid request body. Required fields: name, slug, vendor_id'
      }, { status: 400 });
    }

    const supabase = await createServerAdminClient();

    // Remove any fields that don't exist in the products table
    const { image_url, ...cleanedBody } = body;

    // Add timestamps
    const productData = {
      ...cleanedBody,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Create the product
    const { data, error } = await supabase
      .from('products')
      .insert(productData)
      .select()
      .single();

    if (error) {
      console.error('Database error creating product:', error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error creating product:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
