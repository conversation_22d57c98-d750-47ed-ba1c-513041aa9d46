-- Add courier services for international shipping with weight-based pricing

-- 1. Add weight column to products table (if not exists)
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS weight DECIMAL(8,3) CHECK (weight >= 0 AND weight <= 100);

COMMENT ON COLUMN products.weight IS 'Product weight in kilograms (kg) for shipping calculations';

-- 2. Create courier_services table for international shipping
CREATE TABLE IF NOT EXISTS courier_services (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  logo TEXT,
  contact_phone TEXT,
  contact_email TEXT,
  website TEXT,
  base_cost DECIMAL(10,2) NOT NULL DEFAULT 0, -- Base shipping cost
  cost_per_kg DECIMAL(8,2), -- Additional cost per kg (optional for weight-based pricing)
  free_weight_limit DECIMAL(8,3), -- Weight limit before additional charges (optional)
  estimated_delivery_time TEXT DEFAULT '5-7 business days',
  tracking_supported BOOLEAN DEFAULT false,
  countries TEXT[] DEFAULT '{}', -- Array of countries they ship to
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure weight-based pricing is consistent (both or neither)
  CONSTRAINT weight_pricing_check CHECK (
    (cost_per_kg IS NULL AND free_weight_limit IS NULL) OR
    (cost_per_kg IS NOT NULL AND free_weight_limit IS NOT NULL)
  )
);

-- Create indexes for courier services
CREATE INDEX IF NOT EXISTS courier_services_active_idx ON courier_services(is_active);
CREATE INDEX IF NOT EXISTS courier_services_name_idx ON courier_services(name);

-- Add comments
COMMENT ON TABLE courier_services IS 'International courier service providers';
COMMENT ON COLUMN courier_services.base_cost IS 'Base shipping cost regardless of weight';
COMMENT ON COLUMN courier_services.cost_per_kg IS 'Additional cost per kg over free weight limit';
COMMENT ON COLUMN courier_services.free_weight_limit IS 'Weight limit (kg) before additional charges apply';
COMMENT ON COLUMN courier_services.countries IS 'Array of countries/regions they ship to';

-- 3. Create product_shipping table (links products to shipping services)
CREATE TABLE IF NOT EXISTS product_shipping (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  shipping_type VARCHAR(20) NOT NULL CHECK (shipping_type IN ('local', 'international')),
  delivery_service_id UUID REFERENCES external_delivery_services(id), -- For local shipping
  courier_service_id UUID REFERENCES courier_services(id), -- For international shipping
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure only one shipping configuration per product
  UNIQUE(product_id),
  
  -- Ensure proper service selection based on shipping type
  CONSTRAINT shipping_service_check CHECK (
    (shipping_type = 'local' AND delivery_service_id IS NOT NULL AND courier_service_id IS NULL) OR
    (shipping_type = 'international' AND courier_service_id IS NOT NULL AND delivery_service_id IS NULL)
  )
);

-- Create indexes for product_shipping
CREATE INDEX IF NOT EXISTS product_shipping_product_id_idx ON product_shipping(product_id);
CREATE INDEX IF NOT EXISTS product_shipping_shipping_type_idx ON product_shipping(shipping_type);
CREATE INDEX IF NOT EXISTS product_shipping_delivery_service_idx ON product_shipping(delivery_service_id);
CREATE INDEX IF NOT EXISTS product_shipping_courier_service_idx ON product_shipping(courier_service_id);

-- Add comments
COMMENT ON TABLE product_shipping IS 'Links products to their shipping configuration (local or international)';
COMMENT ON COLUMN product_shipping.shipping_type IS 'Type of shipping: local (uses delivery services) or international (uses courier services)';

-- 4. Create updated_at triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to courier services
DROP TRIGGER IF EXISTS update_courier_services_updated_at ON courier_services;
CREATE TRIGGER update_courier_services_updated_at
    BEFORE UPDATE ON courier_services
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_product_shipping_updated_at ON product_shipping;
CREATE TRIGGER update_product_shipping_updated_at
    BEFORE UPDATE ON product_shipping
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Show completion status
SELECT 'COURIER SERVICES SETUP COMPLETE' as status;