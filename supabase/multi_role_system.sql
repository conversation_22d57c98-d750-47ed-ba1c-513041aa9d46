-- Multi-Role Access Control System Setup
-- This script sets up the three-role system: user, store_owner, admin

-- 1. Ensure role column exists with proper constraints
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'profiles' AND column_name = 'role'
  ) THEN
    ALTER TABLE profiles ADD COLUMN role TEXT NOT NULL DEFAULT 'user';
    CREATE INDEX IF NOT EXISTS profiles_role_idx ON profiles(role);
    RAISE NOTICE 'Added role column to profiles table';
  ELSE
    RAISE NOTICE 'Role column already exists in profiles table';
  END IF;
END
$$;

-- 2. Add role constraint to ensure only valid roles
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS profiles_role_check;
ALTER TABLE profiles ADD CONSTRAINT profiles_role_check
  CHECK (role IN ('user', 'store_owner', 'admin'));

-- 3. Add store_owner relationship to stores table
ALTER TABLE stores ADD COLUMN IF NOT EXISTS owner_id UUID REFERENCES auth.users(id);
CREATE INDEX IF NOT EXISTS stores_owner_id_idx ON stores(owner_id);

-- 4. Create order_store_items table for tracking which store each order item belongs to
CREATE TABLE IF NOT EXISTS order_store_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  store_id UUID NOT NULL REFERENCES stores(id),
  total_amount DECIMAL(10, 2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'GMD',
  status TEXT NOT NULL DEFAULT 'pending', -- pending, accepted, rejected, completed
  rejection_reason TEXT,
  rejection_notes TEXT,
  accepted_at TIMESTAMP WITH TIME ZONE,
  rejected_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(order_id, store_id)
);

-- Create indexes for order_store_items
CREATE INDEX IF NOT EXISTS order_store_items_order_id_idx ON order_store_items(order_id);
CREATE INDEX IF NOT EXISTS order_store_items_store_id_idx ON order_store_items(store_id);
CREATE INDEX IF NOT EXISTS order_store_items_status_idx ON order_store_items(status);

-- 5. Update notifications table for role-based notifications
-- The table already exists, so we'll add missing columns if they don't exist

-- Add message column (alias for content) if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'notifications' AND column_name = 'message'
  ) THEN
    ALTER TABLE notifications ADD COLUMN message TEXT;
    -- Copy content to message for existing records
    UPDATE notifications SET message = content WHERE message IS NULL;
    -- Make message NOT NULL after copying data
    ALTER TABLE notifications ALTER COLUMN message SET NOT NULL;
    RAISE NOTICE 'Added message column to notifications table';
  ELSE
    RAISE NOTICE 'Message column already exists in notifications table';
  END IF;
END
$$;

-- Add notification_category column if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'notifications' AND column_name = 'notification_category'
  ) THEN
    ALTER TABLE notifications ADD COLUMN notification_category TEXT NOT NULL DEFAULT 'general';
    CREATE INDEX IF NOT EXISTS notifications_category_idx ON notifications(notification_category);
    RAISE NOTICE 'Added notification_category column to notifications table';
  ELSE
    RAISE NOTICE 'Notification_category column already exists in notifications table';
  END IF;
END
$$;

-- Add updated_at column if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'notifications' AND column_name = 'updated_at'
  ) THEN
    ALTER TABLE notifications ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    RAISE NOTICE 'Added updated_at column to notifications table';
  ELSE
    RAISE NOTICE 'Updated_at column already exists in notifications table';
  END IF;
END
$$;

-- Ensure existing indexes exist
CREATE INDEX IF NOT EXISTS notifications_user_id_idx ON notifications(user_id);
CREATE INDEX IF NOT EXISTS notifications_read_idx ON notifications(read);
CREATE INDEX IF NOT EXISTS notifications_type_idx ON notifications(type);
CREATE INDEX IF NOT EXISTS notifications_created_at_idx ON notifications(created_at);

-- 6. Update RLS policies for multi-role access

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Admins can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can update all profiles" ON profiles;
DROP POLICY IF EXISTS "Admin email can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Admin email can update all profiles" ON profiles;

-- Profiles policies
CREATE POLICY "Users can view own profile"
  ON profiles FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can update own profile"
  ON profiles FOR UPDATE
  USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles"
  ON profiles FOR SELECT
  TO authenticated
  USING (
    auth.uid() = id OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Admins can update all profiles"
  ON profiles FOR UPDATE
  TO authenticated
  USING (
    auth.uid() = id OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Stores policies
CREATE POLICY "Anyone can view active stores"
  ON stores FOR SELECT
  USING (status = 'active' OR status IS NULL);

CREATE POLICY "Store owners can view their stores"
  ON stores FOR SELECT
  TO authenticated
  USING (
    owner_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Store owners can update their stores"
  ON stores FOR UPDATE
  TO authenticated
  USING (
    owner_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Admins can insert stores"
  ON stores FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Products policies
CREATE POLICY "Anyone can view products"
  ON products FOR SELECT
  USING (true);

CREATE POLICY "Store owners can manage their products"
  ON products FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM stores
      WHERE id = store_id AND owner_id = auth.uid()
    ) OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Orders policies
CREATE POLICY "Users can view their orders"
  ON orders FOR SELECT
  TO authenticated
  USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Users can create their orders"
  ON orders FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Admins can update orders"
  ON orders FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Order store items policies
CREATE POLICY "Store owners can view their order items"
  ON order_store_items FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM stores
      WHERE id = store_id AND owner_id = auth.uid()
    ) OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Store owners can update their order items"
  ON order_store_items FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM stores
      WHERE id = store_id AND owner_id = auth.uid()
    ) OR
    EXISTS (
      SELECT 1 FROM profiles
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Notifications policies
CREATE POLICY "Users can view their notifications"
  ON notifications FOR SELECT
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "Users can update their notifications"
  ON notifications FOR UPDATE
  TO authenticated
  USING (user_id = auth.uid());

CREATE POLICY "System can insert notifications"
  ON notifications FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- 7. Enable RLS on all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE stores ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_store_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- 8. Set up your admin user
UPDATE profiles
SET role = 'admin'
WHERE email = '<EMAIL>';

-- 9. Set up store owner
UPDATE profiles
SET role = 'store_owner'
WHERE email = '<EMAIL>';

-- 10. Create function to automatically create order_store_items when order_items are created
CREATE OR REPLACE FUNCTION create_order_store_items()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert or update order_store_items for this store
  INSERT INTO order_store_items (order_id, store_id, total_amount, currency)
  VALUES (NEW.order_id, NEW.store_id, NEW.total, NEW.currency)
  ON CONFLICT (order_id, store_id)
  DO UPDATE SET
    total_amount = order_store_items.total_amount + NEW.total,
    updated_at = NOW();

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for order_items
DROP TRIGGER IF EXISTS create_order_store_items_trigger ON order_items;
CREATE TRIGGER create_order_store_items_trigger
  AFTER INSERT ON order_items
  FOR EACH ROW
  EXECUTE FUNCTION create_order_store_items();

-- 11. Create function to send notifications
CREATE OR REPLACE FUNCTION send_notification(
  p_user_id UUID,
  p_title TEXT,
  p_message TEXT,
  p_type TEXT DEFAULT 'info',
  p_category TEXT DEFAULT 'general',
  p_data JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  notification_id UUID;
BEGIN
  INSERT INTO notifications (user_id, title, content, message, type, notification_category, data)
  VALUES (p_user_id, p_title, p_message, p_message, p_type, p_category, p_data)
  RETURNING id INTO notification_id;

  RETURN notification_id;
END;
$$ LANGUAGE plpgsql;

COMMIT;
