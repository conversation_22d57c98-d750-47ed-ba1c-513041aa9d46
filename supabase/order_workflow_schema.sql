-- Order Workflow Database Schema
-- Run this script in your Supabase SQL editor

-- Update orders table to include new status values
ALTER TABLE orders 
ALTER COLUMN status TYPE TEXT;

-- Add check constraint for valid order statuses
ALTER TABLE orders 
DROP CONSTRAINT IF EXISTS orders_status_check;

ALTER TABLE orders 
ADD CONSTRAINT orders_status_check 
CHECK (status IN ('pending', 'under_review', 'awaiting_store_confirmation', 'accepted_by_store', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'));

-- Create order_store_items table if it doesn't exist
CREATE TABLE IF NOT EXISTS order_store_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  store_id UUID NOT NULL REFERENCES stores(id),
  total_amount DECIMAL(10, 2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'GMD',
  status TEXT NOT NULL DEFAULT 'pending',
  rejection_reason TEXT,
  rejection_notes TEXT,
  accepted_at TIMESTAMP WITH TIME ZONE,
  rejected_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(order_id, store_id)
);

-- Add check constraint for order_store_items status
ALTER TABLE order_store_items 
DROP CONSTRAINT IF EXISTS order_store_items_status_check;

ALTER TABLE order_store_items 
ADD CONSTRAINT order_store_items_status_check 
CHECK (status IN ('pending', 'under_review', 'awaiting_store_confirmation', 'accepted', 'rejected', 'completed'));

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS order_store_items_order_id_idx ON order_store_items(order_id);
CREATE INDEX IF NOT EXISTS order_store_items_store_id_idx ON order_store_items(store_id);
CREATE INDEX IF NOT EXISTS order_store_items_status_idx ON order_store_items(status);

-- Update notifications table to include new notification types
ALTER TABLE notifications 
DROP CONSTRAINT IF EXISTS notifications_type_check;

ALTER TABLE notifications 
ADD CONSTRAINT notifications_type_check 
CHECK (type IN ('order', 'payment', 'order_review', 'store_confirmation', 'payment_confirmed', 'general'));

-- Function to automatically create order_store_items when an order is created
CREATE OR REPLACE FUNCTION create_order_store_items()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert order_store_items for each unique store in the order
  INSERT INTO order_store_items (order_id, store_id, total_amount, currency, status)
  SELECT 
    NEW.id,
    oi.store_id,
    SUM(oi.total),
    NEW.currency,
    'pending'
  FROM order_items oi
  WHERE oi.order_id = NEW.id
  GROUP BY oi.store_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically create order_store_items
DROP TRIGGER IF EXISTS create_order_store_items_trigger ON orders;
CREATE TRIGGER create_order_store_items_trigger
  AFTER INSERT ON orders
  FOR EACH ROW
  EXECUTE FUNCTION create_order_store_items();

-- Function to update order_store_items when order_items change
CREATE OR REPLACE FUNCTION update_order_store_items()
RETURNS TRIGGER AS $$
BEGIN
  -- Update or insert order_store_items
  INSERT INTO order_store_items (order_id, store_id, total_amount, currency, status)
  SELECT 
    COALESCE(NEW.order_id, OLD.order_id),
    COALESCE(NEW.store_id, OLD.store_id),
    COALESCE(SUM(oi.total), 0),
    (SELECT currency FROM orders WHERE id = COALESCE(NEW.order_id, OLD.order_id)),
    'pending'
  FROM order_items oi
  WHERE oi.order_id = COALESCE(NEW.order_id, OLD.order_id)
    AND oi.store_id = COALESCE(NEW.store_id, OLD.store_id)
  GROUP BY oi.store_id
  ON CONFLICT (order_id, store_id)
  DO UPDATE SET
    total_amount = EXCLUDED.total_amount,
    updated_at = NOW();
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for order_items changes
DROP TRIGGER IF EXISTS update_order_store_items_trigger ON order_items;
CREATE TRIGGER update_order_store_items_trigger
  AFTER INSERT OR UPDATE OR DELETE ON order_items
  FOR EACH ROW
  EXECUTE FUNCTION update_order_store_items();

-- Add role column to profiles table if it doesn't exist
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'user';

-- Add check constraint for valid roles
ALTER TABLE profiles 
DROP CONSTRAINT IF EXISTS profiles_role_check;

ALTER TABLE profiles 
ADD CONSTRAINT profiles_role_check 
CHECK (role IN ('user', 'store_owner', 'admin'));

-- Create index on role for better performance
CREATE INDEX IF NOT EXISTS profiles_role_idx ON profiles(role);

-- Update existing profiles to have proper roles (you may need to adjust this)
-- UPDATE profiles SET role = 'admin' WHERE email = '<EMAIL>';
-- UPDATE profiles SET role = 'store_owner' WHERE email = '<EMAIL>';

-- Add owner_id to stores table if it doesn't exist
ALTER TABLE stores 
ADD COLUMN IF NOT EXISTS owner_id UUID REFERENCES auth.users(id);

-- Create index on owner_id for better performance
CREATE INDEX IF NOT EXISTS stores_owner_id_idx ON stores(owner_id);

-- Add status column to stores table if it doesn't exist
ALTER TABLE stores 
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active';

-- Add check constraint for store status
ALTER TABLE stores 
DROP CONSTRAINT IF EXISTS stores_status_check;

ALTER TABLE stores 
ADD CONSTRAINT stores_status_check 
CHECK (status IN ('active', 'disabled', 'suspended'));

-- Ensure order_items has store_id column
ALTER TABLE order_items 
ADD COLUMN IF NOT EXISTS store_id UUID REFERENCES stores(id);

-- Create index on store_id for better performance
CREATE INDEX IF NOT EXISTS order_items_store_id_idx ON order_items(store_id);

-- Grant necessary permissions
GRANT ALL ON order_store_items TO authenticated;
GRANT ALL ON order_store_items TO anon;

-- Row Level Security policies for order_store_items
ALTER TABLE order_store_items ENABLE ROW LEVEL SECURITY;

-- Store owners can view their own store's order items
CREATE POLICY "Store owners can view their store order items" ON order_store_items
  FOR SELECT USING (
    store_id IN (
      SELECT id FROM stores WHERE owner_id = auth.uid()
    )
  );

-- Store owners can update their own store's order items
CREATE POLICY "Store owners can update their store order items" ON order_store_items
  FOR UPDATE USING (
    store_id IN (
      SELECT id FROM stores WHERE owner_id = auth.uid()
    )
  );

-- Admins can view all order store items
CREATE POLICY "Admins can view all order store items" ON order_store_items
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

COMMIT;
