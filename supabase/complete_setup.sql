-- Complete setup script for role-based access
-- This script sets up everything needed for the multi-role system

BEGIN;

-- 1. First, disable <PERSON><PERSON> temporarily to avoid issues
ALTER TABLE stores DISABLE ROW LEVEL SECURITY;
ALTER TABLE products DISABLE ROW LEVEL SECURITY;
ALTER TABLE categories DISABLE ROW LEVEL SECURITY;
ALTER TABLE notifications DISABLE ROW LEVEL SECURITY;
ALTER TABLE orders DISABLE ROW LEVEL SECURITY;
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- 2. Set up user roles
-- <EMAIL> = admin (can access /admin)
-- <EMAIL> = store_owner (can access /store-admin)

UPDATE profiles 
SET role = 'admin' 
WHERE email = '<EMAIL>';

UPDATE profiles 
SET role = 'store_owner' 
WHERE email = '<EMAIL>';

-- 3. Assign <EMAIL> as owner of the store
UPDATE stores 
SET owner_id = (
  SELECT id FROM profiles WHERE email = '<EMAIL>' LIMIT 1
)
WHERE id = 'ec0f0876-136d-4913-8ccd-14847ba516f1'
AND EXISTS (SELECT 1 FROM profiles WHERE email = '<EMAIL>');

-- 4. Re-enable RLS with simple policies
ALTER TABLE stores ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 5. Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can update all profiles" ON profiles;
DROP POLICY IF EXISTS "admin_email_access" ON profiles;
DROP POLICY IF EXISTS "users_own_profile_select" ON profiles;
DROP POLICY IF EXISTS "users_own_profile_update" ON profiles;
DROP POLICY IF EXISTS "users_own_profile_insert" ON profiles;

DROP POLICY IF EXISTS "Public can view all stores" ON stores;
DROP POLICY IF EXISTS "Anyone can view active stores" ON stores;
DROP POLICY IF EXISTS "allow_all_stores_select" ON stores;
DROP POLICY IF EXISTS "store_owners_manage_stores_simple" ON stores;

DROP POLICY IF EXISTS "Public can view all products" ON products;
DROP POLICY IF EXISTS "Anyone can view products" ON products;
DROP POLICY IF EXISTS "allow_all_products_select" ON products;
DROP POLICY IF EXISTS "store_owners_manage_products_simple" ON products;

DROP POLICY IF EXISTS "Public can view categories" ON categories;
DROP POLICY IF EXISTS "Anyone can view categories" ON categories;
DROP POLICY IF EXISTS "allow_all_categories_select" ON categories;
DROP POLICY IF EXISTS "admins_manage_categories_simple" ON categories;

-- 6. Create simple, working policies

-- Profiles: Users can see their own, admins can see all
CREATE POLICY "profiles_own_access" ON profiles
  FOR ALL
  USING (
    auth.uid() = id OR 
    auth.email() = '<EMAIL>'
  );

-- Stores: Public can view all stores
CREATE POLICY "stores_public_view" ON stores
  FOR SELECT
  USING (true);

-- Store management: Store owners and admins can manage
CREATE POLICY "stores_owner_manage" ON stores
  FOR ALL
  TO authenticated
  USING (
    owner_id = auth.uid() OR 
    auth.email() = '<EMAIL>'
  );

-- Products: Public can view all products
CREATE POLICY "products_public_view" ON products
  FOR SELECT
  USING (true);

-- Product management: Store owners and admins can manage their products
CREATE POLICY "products_owner_manage" ON products
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM stores 
      WHERE id = products.store_id AND owner_id = auth.uid()
    ) OR
    auth.email() = '<EMAIL>'
  );

-- Categories: Public can view all categories
CREATE POLICY "categories_public_view" ON categories
  FOR SELECT
  USING (true);

-- Category management: Only admins can manage categories
CREATE POLICY "categories_admin_manage" ON categories
  FOR ALL
  TO authenticated
  USING (auth.email() = '<EMAIL>');

-- Orders: Users can see their own orders, store owners can see orders for their stores
CREATE POLICY "orders_user_access" ON orders
  FOR SELECT
  TO authenticated
  USING (
    user_id = auth.uid() OR
    auth.email() = '<EMAIL>'
  );

CREATE POLICY "orders_user_create" ON orders
  FOR INSERT
  TO authenticated
  WITH CHECK (user_id = auth.uid());

-- Notifications: Users can see their own notifications
CREATE POLICY "notifications_user_access" ON notifications
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());

-- 7. Handle other tables if they exist
DO $$
BEGIN
  -- Order items
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'order_items') THEN
    ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
    DROP POLICY IF EXISTS "order_items_access" ON order_items;
    
    CREATE POLICY "order_items_access" ON order_items
      FOR ALL
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM orders 
          WHERE id = order_items.order_id AND user_id = auth.uid()
        ) OR
        EXISTS (
          SELECT 1 FROM stores 
          WHERE id = order_items.store_id AND owner_id = auth.uid()
        ) OR
        auth.email() = '<EMAIL>'
      );
  END IF;

  -- Order store items
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'order_store_items') THEN
    ALTER TABLE order_store_items ENABLE ROW LEVEL SECURITY;
    DROP POLICY IF EXISTS "order_store_items_access" ON order_store_items;
    
    CREATE POLICY "order_store_items_access" ON order_store_items
      FOR ALL
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM stores 
          WHERE id = order_store_items.store_id AND owner_id = auth.uid()
        ) OR
        auth.email() = '<EMAIL>'
      );
  END IF;

  -- Product images
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'product_images') THEN
    ALTER TABLE product_images ENABLE ROW LEVEL SECURITY;
    DROP POLICY IF EXISTS "product_images_public" ON product_images;
    
    CREATE POLICY "product_images_public" ON product_images
      FOR SELECT
      USING (true);
      
    CREATE POLICY "product_images_manage" ON product_images
      FOR ALL
      TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM products p
          JOIN stores s ON p.store_id = s.id
          WHERE p.id = product_images.product_id AND s.owner_id = auth.uid()
        ) OR
        auth.email() = '<EMAIL>'
      );
  END IF;
END
$$;

-- 8. Verify the setup
SELECT 
  'User Roles' as check_type,
  p.email, 
  p.role, 
  p.id,
  s.name as store_name,
  s.id as store_id
FROM profiles p
LEFT JOIN stores s ON s.owner_id = p.id
WHERE p.email IN ('<EMAIL>', '<EMAIL>')
ORDER BY p.email;

COMMIT;

-- Show final status
SELECT 
  'RLS Status' as info,
  schemaname,
  tablename,
  rowsecurity
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('profiles', 'stores', 'products', 'categories', 'orders', 'notifications')
ORDER BY tablename;
