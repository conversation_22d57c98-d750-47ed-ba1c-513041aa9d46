-- Enhanced Order System with Inventory Management and Service Fees
-- Run this script in your Supabase SQL editor

-- 1. Add inventory_quantity column to products if it doesn't exist
ALTER TABLE products
ADD COLUMN IF NOT EXISTS inventory_quantity INTEGER DEFAULT 100;

-- 2. Add delivery method and service fee columns to orders
ALTER TABLE orders
ADD COLUMN IF NOT EXISTS delivery_method TEXT DEFAULT 'delivery' CHECK (delivery_method IN ('delivery', 'pickup'));

ALTER TABLE orders
ADD COLUMN IF NOT EXISTS service_fee DECIMAL(10, 2) DEFAULT 0;

ALTER TABLE orders
ADD COLUMN IF NOT EXISTS subtotal DECIMAL(10, 2);

-- 3. <PERSON><PERSON> confirmed_payments table to track payment confirmations
CREATE TABLE IF NOT EXISTS confirmed_payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  payment_id UUID NOT NULL REFERENCES payments(id) ON DELETE CASCADE,
  confirmed_by UUID NOT NULL REFERENCES auth.users(id),
  confirmed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  inventory_updated BOOLEAN DEFAULT FALSE,
  store_notified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(order_id, payment_id)
);

-- 4. Create payout_schedule table for tracking Monday/Friday payouts
CREATE TABLE IF NOT EXISTS payout_schedule (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  store_id UUID NOT NULL REFERENCES stores(id),
  payout_date DATE NOT NULL,
  total_amount DECIMAL(10, 2) NOT NULL DEFAULT 0,
  service_fees_deducted DECIMAL(10, 2) NOT NULL DEFAULT 0,
  net_amount DECIMAL(10, 2) NOT NULL DEFAULT 0,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(store_id, payout_date)
);

-- 5. Create inventory_transactions table for tracking inventory changes
CREATE TABLE IF NOT EXISTS inventory_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  product_id UUID NOT NULL REFERENCES products(id),
  order_id UUID REFERENCES orders(id),
  transaction_type TEXT NOT NULL CHECK (transaction_type IN ('sale', 'restock', 'adjustment')),
  quantity_change INTEGER NOT NULL,
  previous_quantity INTEGER NOT NULL,
  new_quantity INTEGER NOT NULL,
  reason TEXT,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Add indexes for better performance
CREATE INDEX IF NOT EXISTS confirmed_payments_order_id_idx ON confirmed_payments(order_id);
CREATE INDEX IF NOT EXISTS confirmed_payments_payment_id_idx ON confirmed_payments(payment_id);
CREATE INDEX IF NOT EXISTS payout_schedule_store_id_idx ON payout_schedule(store_id);
CREATE INDEX IF NOT EXISTS payout_schedule_payout_date_idx ON payout_schedule(payout_date);
CREATE INDEX IF NOT EXISTS inventory_transactions_product_id_idx ON inventory_transactions(product_id);
CREATE INDEX IF NOT EXISTS inventory_transactions_order_id_idx ON inventory_transactions(order_id);

-- 7. Function to calculate service fee based on product price
CREATE OR REPLACE FUNCTION calculate_service_fee(subtotal DECIMAL)
RETURNS DECIMAL AS $$
BEGIN
  IF subtotal > 201 THEN
    RETURN 5.00;
  ELSE
    RETURN 2.00;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- 8. Function to update inventory when payment is confirmed
CREATE OR REPLACE FUNCTION update_inventory_on_payment_confirmation()
RETURNS TRIGGER AS $$
DECLARE
  order_item RECORD;
  current_inventory INTEGER;
BEGIN
  -- Only process if this is a new confirmed payment
  IF TG_OP = 'INSERT' THEN
    -- Get all order items for this order
    FOR order_item IN
      SELECT oi.product_id, oi.quantity, p.inventory_quantity
      FROM order_items oi
      JOIN products p ON p.id = oi.product_id
      WHERE oi.order_id = NEW.order_id
    LOOP
      -- Check if we have enough inventory
      IF order_item.inventory_quantity >= order_item.quantity THEN
        -- Update product inventory
        UPDATE products
        SET inventory_quantity = inventory_quantity - order_item.quantity,
            updated_at = NOW()
        WHERE id = order_item.product_id;

        -- Record inventory transaction
        INSERT INTO inventory_transactions (
          product_id,
          order_id,
          transaction_type,
          quantity_change,
          previous_quantity,
          new_quantity,
          reason,
          created_by
        ) VALUES (
          order_item.product_id,
          NEW.order_id,
          'sale',
          -order_item.quantity,
          order_item.inventory_quantity,
          order_item.inventory_quantity - order_item.quantity,
          'Payment confirmed - inventory reduced',
          NEW.confirmed_by
        );
      ELSE
        -- Log warning but don't fail the transaction
        RAISE WARNING 'Insufficient inventory for product % (available: %, requested: %)',
          order_item.product_id, order_item.inventory_quantity, order_item.quantity;
      END IF;
    END LOOP;

    -- Mark inventory as updated
    UPDATE confirmed_payments
    SET inventory_updated = TRUE
    WHERE id = NEW.id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 9. Create trigger for inventory updates
DROP TRIGGER IF EXISTS trigger_update_inventory_on_payment_confirmation ON confirmed_payments;
CREATE TRIGGER trigger_update_inventory_on_payment_confirmation
  AFTER INSERT ON confirmed_payments
  FOR EACH ROW
  EXECUTE FUNCTION update_inventory_on_payment_confirmation();

-- 10. Function to generate payout schedules for upcoming Mondays and Fridays
CREATE OR REPLACE FUNCTION generate_payout_schedule()
RETURNS VOID AS $$
DECLARE
  next_monday DATE;
  next_friday DATE;
  store_record RECORD;
BEGIN
  -- Calculate next Monday and Friday
  next_monday := CASE
    WHEN EXTRACT(DOW FROM CURRENT_DATE) = 1 THEN CURRENT_DATE + INTERVAL '7 days'
    WHEN EXTRACT(DOW FROM CURRENT_DATE) < 1 THEN CURRENT_DATE + INTERVAL '1 day' * (1 - EXTRACT(DOW FROM CURRENT_DATE))
    ELSE CURRENT_DATE + INTERVAL '1 day' * (8 - EXTRACT(DOW FROM CURRENT_DATE))
  END;

  next_friday := CASE
    WHEN EXTRACT(DOW FROM CURRENT_DATE) = 5 THEN CURRENT_DATE + INTERVAL '7 days'
    WHEN EXTRACT(DOW FROM CURRENT_DATE) < 5 THEN CURRENT_DATE + INTERVAL '1 day' * (5 - EXTRACT(DOW FROM CURRENT_DATE))
    ELSE CURRENT_DATE + INTERVAL '1 day' * (12 - EXTRACT(DOW FROM CURRENT_DATE))
  END;

  -- Create payout schedule entries for all active stores
  FOR store_record IN SELECT id FROM stores WHERE status = 'active' LOOP
    -- Insert Monday payout if not exists
    INSERT INTO payout_schedule (store_id, payout_date)
    VALUES (store_record.id, next_monday)
    ON CONFLICT (store_id, payout_date) DO NOTHING;

    -- Insert Friday payout if not exists
    INSERT INTO payout_schedule (store_id, payout_date)
    VALUES (store_record.id, next_friday)
    ON CONFLICT (store_id, payout_date) DO NOTHING;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 11. Update existing products to have inventory if they don't
UPDATE products
SET inventory_quantity = 100
WHERE inventory_quantity IS NULL OR inventory_quantity = 0;

-- 12. Generate initial payout schedule
SELECT generate_payout_schedule();
