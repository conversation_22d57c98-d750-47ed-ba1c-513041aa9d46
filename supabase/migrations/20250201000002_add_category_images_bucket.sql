-- Create storage bucket for category images
INSERT INTO storage.buckets (id, name, public, avif_autodetection)
VALUES ('category-images', 'category-images', true, false)
ON CONFLICT (id) DO NOTHING;

-- Set up security policies for the category-images bucket
-- Allow public read access to category images
CREATE POLICY "Public Access to Category Images"
ON storage.objects FOR SELECT
USING (bucket_id = 'category-images');

-- Allow authenticated users to upload category images
CREATE POLICY "Authenticated users can upload category images"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (bucket_id = 'category-images');

-- Allow authenticated users to update category images
CREATE POLICY "Authenticated users can update category images"
ON storage.objects FOR UPDATE
TO authenticated
USING (bucket_id = 'category-images')
WITH CHECK (bucket_id = 'category-images');

-- Allow authenticated users to delete category images
CREATE POLICY "Authenticated users can delete category images"
ON storage.objects FOR DELETE
TO authenticated
USING (bucket_id = 'category-images');
