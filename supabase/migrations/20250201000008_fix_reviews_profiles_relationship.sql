-- Fix the relationship between reviews and profiles tables
-- The reviews table references auth.users(id) via user_id
-- The profiles table also references auth.users(id) via id
-- We need to create a proper foreign key relationship

-- First, let's add a foreign key constraint from reviews.user_id to profiles.id
-- Since both reference auth.users(id), this should work
ALTER TABLE reviews 
ADD CONSTRAINT reviews_user_id_profiles_fkey 
FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;

-- Add an index for better performance
CREATE INDEX IF NOT EXISTS reviews_user_id_profiles_idx ON reviews(user_id);

-- Update the reviews table to make product_id nullable for store-only reviews
-- (This might already be done in a previous migration, but let's ensure it)
ALTER TABLE reviews ALTER COLUMN product_id DROP NOT NULL;

-- Add a constraint to ensure either product_id is provided (product review) 
-- or it's null (store-only review)
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_product_or_store_check;
ALTER TABLE reviews ADD CONSTRAINT reviews_product_or_store_check 
CHECK (product_id IS NOT NULL OR store_id IS NOT NULL);

-- Update the unique constraint to handle both product reviews and store-only reviews
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_user_id_product_id_key;
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_user_product_or_store_unique;
ALTER TABLE reviews ADD CONSTRAINT reviews_user_product_or_store_unique 
UNIQUE(user_id, COALESCE(product_id, store_id));