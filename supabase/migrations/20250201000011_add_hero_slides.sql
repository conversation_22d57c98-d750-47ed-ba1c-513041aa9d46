-- Create hero_slides table for managing homepage slider content
CREATE TABLE IF NOT EXISTS hero_slides (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  subtitle TEXT,
  image_url TEXT NOT NULL,
  link_url TEXT,
  link_text TEXT DEFAULT 'Learn More',
  background_color TEXT DEFAULT '#F78100',
  text_color TEXT DEFAULT '#FFFFFF',
  position INTEGER NOT NULL DEFAULT 0,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON>reate indexes on hero_slides
CREATE INDEX IF NOT EXISTS hero_slides_position_idx ON hero_slides(position);
CREATE INDEX IF NOT EXISTS hero_slides_active_idx ON hero_slides(is_active);

-- Insert default slider content (same as current hardcoded data)
INSERT INTO hero_slides (title, subtitle, image_url, link_url, link_text, position, is_active) VALUES
  ('Motors', 'Find the perfect vehicle for your needs', 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?ixlib=rb-4.0.3', '/categories/motors', 'Find now →', 1, true),
  ('Electronics', 'Discover the latest tech and gadgets', 'https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?ixlib=rb-4.0.3', '/categories/electronics', 'Find now →', 2, true),
  ('Collectibles and Art', 'Unique items and artistic treasures', 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?ixlib=rb-4.0.3', '/categories/collectibles-art', 'Find now →', 3, true)
ON CONFLICT DO NOTHING;

-- Add RLS policies for hero_slides
ALTER TABLE hero_slides ENABLE ROW LEVEL SECURITY;

-- Allow public read access to active slides
CREATE POLICY "Allow public read access to active hero slides" ON hero_slides
  FOR SELECT USING (is_active = true);

-- Allow admin users to manage slides
CREATE POLICY "Allow admin users to manage hero slides" ON hero_slides
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );
