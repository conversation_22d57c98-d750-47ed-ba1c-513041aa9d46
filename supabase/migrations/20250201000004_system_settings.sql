-- Create system_settings table for configurable application settings
CREATE TABLE IF NOT EXISTS system_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  setting_key TEXT UNIQUE NOT NULL,
  setting_value JSONB NOT NULL,
  description TEXT,
  category TEXT DEFAULT 'general',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  updated_by UUID REFERENCES auth.users(id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS system_settings_key_idx ON system_settings(setting_key);
CREATE INDEX IF NOT EXISTS system_settings_category_idx ON system_settings(category);
CREATE INDEX IF NOT EXISTS system_settings_active_idx ON system_settings(is_active);

-- Enable RLS
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- Create policies - only admins can manage system settings
CREATE POLICY "Ad<PERSON> can view system settings"
  ON system_settings FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Admins can insert system settings"
  ON system_settings FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

CREATE POLICY "Admins can update system settings"
  ON system_settings FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Insert default service fee settings
INSERT INTO system_settings (setting_key, setting_value, description, category, created_by) VALUES
('service_fee_config', '{
  "fee_type": "tiered",
  "fixed_fee": 5.00,
  "percentage_fee": 3.0,
  "tiers": [
    {"min_amount": 0, "max_amount": 200, "fee": 2.00, "percentage": null},
    {"min_amount": 201, "max_amount": null, "fee": 5.00, "percentage": null}
  ],
  "effective_date": null,
  "grace_period_days": 7
}', 'Service fee configuration for transactions', 'payments', (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1))
ON CONFLICT (setting_key) DO NOTHING;

-- Create function to get system setting
CREATE OR REPLACE FUNCTION get_system_setting(key_name TEXT)
RETURNS JSONB AS $$
BEGIN
  RETURN (
    SELECT setting_value 
    FROM system_settings 
    WHERE setting_key = key_name 
    AND is_active = true
    LIMIT 1
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update system setting
CREATE OR REPLACE FUNCTION update_system_setting(
  key_name TEXT,
  new_value JSONB,
  user_id UUID DEFAULT auth.uid()
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user is admin
  IF NOT EXISTS (
    SELECT 1 FROM profiles 
    WHERE profiles.id = user_id 
    AND profiles.role = 'admin'
  ) THEN
    RAISE EXCEPTION 'Access denied: Admin role required';
  END IF;

  -- Update or insert setting
  INSERT INTO system_settings (setting_key, setting_value, updated_by, updated_at)
  VALUES (key_name, new_value, user_id, NOW())
  ON CONFLICT (setting_key) 
  DO UPDATE SET 
    setting_value = EXCLUDED.setting_value,
    updated_by = EXCLUDED.updated_by,
    updated_at = EXCLUDED.updated_at;

  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_system_settings_updated_at
  BEFORE UPDATE ON system_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();