-- Add thumbnail field to categories table if it doesn't exist
ALTER TABLE categories ADD COLUMN IF NOT EXISTS thumbnail TEXT;

-- Add indexes for better hierarchical queries
CREATE INDEX IF NOT EXISTS categories_parent_id_idx ON categories(parent_id);
CREATE INDEX IF NOT EXISTS categories_featured_idx ON categories(featured);

-- Insert sample hierarchical categories
-- Only insert if they don't already exist to avoid conflicts
-- We'll use INSERT ... ON CONFLICT DO NOTHING to safely add sample data

-- Insert parent categories (let database generate UUIDs)
INSERT INTO categories (name, slug, description, featured, created_at, updated_at) VALUES
('Fashion', 'fashion', 'Clothing, shoes, and fashion accessories', true, NOW(), NOW()),
('Electronics', 'electronics', 'Electronic devices and gadgets', true, NOW(), NOW()),
('Home & Garden', 'home-garden', 'Home improvement and garden supplies', true, NOW(), NOW()),
('Sports & Outdoors', 'sports', 'Sports equipment and outdoor gear', false, NOW(), NOW()),
('Books', 'books', 'Books and educational materials', false, NOW(), NOW())
ON CONFLICT (slug) DO NOTHING;

-- Insert subcategories for Fashion
INSERT INTO categories (name, slug, description, parent_id, featured, created_at, updated_at)
SELECT 'Men', 'men', 'Men''s clothing and accessories', id, false, NOW(), NOW()
FROM categories WHERE slug = 'fashion'
ON CONFLICT (slug) DO NOTHING;

INSERT INTO categories (name, slug, description, parent_id, featured, created_at, updated_at)
SELECT 'Women', 'women', 'Women''s clothing and accessories', id, false, NOW(), NOW()
FROM categories WHERE slug = 'fashion'
ON CONFLICT (slug) DO NOTHING;

INSERT INTO categories (name, slug, description, parent_id, featured, created_at, updated_at)
SELECT 'Kids', 'kids', 'Children''s clothing and accessories', id, false, NOW(), NOW()
FROM categories WHERE slug = 'fashion'
ON CONFLICT (slug) DO NOTHING;

-- Insert subcategories for Electronics
INSERT INTO categories (name, slug, description, parent_id, featured, created_at, updated_at)
SELECT 'Smartphones', 'smartphones', 'Mobile phones and accessories', id, false, NOW(), NOW()
FROM categories WHERE slug = 'electronics'
ON CONFLICT (slug) DO NOTHING;

INSERT INTO categories (name, slug, description, parent_id, featured, created_at, updated_at)
SELECT 'Laptops', 'laptops', 'Laptops and computer accessories', id, false, NOW(), NOW()
FROM categories WHERE slug = 'electronics'
ON CONFLICT (slug) DO NOTHING;

INSERT INTO categories (name, slug, description, parent_id, featured, created_at, updated_at)
SELECT 'Accessories', 'accessories', 'Electronic accessories and gadgets', id, false, NOW(), NOW()
FROM categories WHERE slug = 'electronics'
ON CONFLICT (slug) DO NOTHING;

-- Insert subcategories for Home & Garden
INSERT INTO categories (name, slug, description, parent_id, featured, created_at, updated_at)
SELECT 'Furniture', 'furniture', 'Home and office furniture', id, false, NOW(), NOW()
FROM categories WHERE slug = 'home-garden'
ON CONFLICT (slug) DO NOTHING;

INSERT INTO categories (name, slug, description, parent_id, featured, created_at, updated_at)
SELECT 'Decor', 'decor', 'Home decoration and accessories', id, false, NOW(), NOW()
FROM categories WHERE slug = 'home-garden'
ON CONFLICT (slug) DO NOTHING;

INSERT INTO categories (name, slug, description, parent_id, featured, created_at, updated_at)
SELECT 'Kitchen', 'kitchen', 'Kitchen appliances and utensils', id, false, NOW(), NOW()
FROM categories WHERE slug = 'home-garden'
ON CONFLICT (slug) DO NOTHING;

-- Insert subcategories for Sports & Outdoors
INSERT INTO categories (name, slug, description, parent_id, featured, created_at, updated_at)
SELECT 'Fitness', 'fitness', 'Fitness equipment and gear', id, false, NOW(), NOW()
FROM categories WHERE slug = 'sports'
ON CONFLICT (slug) DO NOTHING;

INSERT INTO categories (name, slug, description, parent_id, featured, created_at, updated_at)
SELECT 'Outdoor', 'outdoor', 'Outdoor activities and camping gear', id, false, NOW(), NOW()
FROM categories WHERE slug = 'sports'
ON CONFLICT (slug) DO NOTHING;

INSERT INTO categories (name, slug, description, parent_id, featured, created_at, updated_at)
SELECT 'Team Sports', 'team-sports', 'Equipment for team sports', id, false, NOW(), NOW()
FROM categories WHERE slug = 'sports'
ON CONFLICT (slug) DO NOTHING;

-- Insert subcategories for Books
INSERT INTO categories (name, slug, description, parent_id, featured, created_at, updated_at)
SELECT 'Fiction', 'fiction', 'Novels and fictional literature', id, false, NOW(), NOW()
FROM categories WHERE slug = 'books'
ON CONFLICT (slug) DO NOTHING;

INSERT INTO categories (name, slug, description, parent_id, featured, created_at, updated_at)
SELECT 'Non-Fiction', 'non-fiction', 'Educational and informational books', id, false, NOW(), NOW()
FROM categories WHERE slug = 'books'
ON CONFLICT (slug) DO NOTHING;

INSERT INTO categories (name, slug, description, parent_id, featured, created_at, updated_at)
SELECT 'Educational', 'educational', 'Textbooks and learning materials', id, false, NOW(), NOW()
FROM categories WHERE slug = 'books'
ON CONFLICT (slug) DO NOTHING;
