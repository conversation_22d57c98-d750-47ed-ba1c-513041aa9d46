-- Fix reviews table to support both product reviews and store-only reviews
-- This ensures the rating system works as intended: product ratings determine store ratings

-- First, make product_id nullable if it isn't already
ALTER TABLE reviews ALTER COLUMN product_id DROP NOT NULL;

-- Drop the old unique constraint that required product_id
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_user_id_product_id_key;

-- Add a new constraint that allows either product reviews or store reviews
-- Users can review each product once, OR review the store once (without a product)
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_user_store_unique;
ALTER TABLE reviews ADD CONSTRAINT reviews_user_product_or_store_unique 
  UNIQUE(user_id, COALESCE(product_id, store_id));

-- Update the trigger function to calculate store ratings from both product reviews and direct store reviews
CREATE OR REPLACE FUNCTION update_store_rating_from_reviews()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the store's average rating and review count from all reviews for this store
  -- This includes both product reviews and direct store reviews
  UPDATE stores 
  SET 
    rating = (
      SELECT ROUND(AVG(rating)::numeric, 2)
      FROM reviews 
      WHERE store_id = COALESCE(NEW.store_id, OLD.store_id)
    ),
    review_count = (
      SELECT COUNT(*)
      FROM reviews 
      WHERE store_id = COALESCE(NEW.store_id, OLD.store_id)
    ),
    updated_at = NOW()
  WHERE id = COALESCE(NEW.store_id, OLD.store_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Ensure the trigger exists
DROP TRIGGER IF EXISTS update_store_rating_from_reviews_trigger ON reviews;
CREATE TRIGGER update_store_rating_from_reviews_trigger
  AFTER INSERT OR UPDATE OR DELETE ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_store_rating_from_reviews();

-- Add an index for better performance on store rating calculations
CREATE INDEX IF NOT EXISTS reviews_store_id_rating_idx ON reviews(store_id, rating);

-- Update existing store ratings based on current reviews
UPDATE stores 
SET 
  rating = COALESCE((
    SELECT ROUND(AVG(rating)::numeric, 2)
    FROM reviews 
    WHERE reviews.store_id = stores.id
  ), 0),
  review_count = COALESCE((
    SELECT COUNT(*)
    FROM reviews 
    WHERE reviews.store_id = stores.id
  ), 0),
  updated_at = NOW();