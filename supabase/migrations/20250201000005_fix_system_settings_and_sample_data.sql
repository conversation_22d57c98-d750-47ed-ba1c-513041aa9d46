-- Fix the missing system settings function and add sample data

-- Create function to update system setting (this was missing)
CREATE OR REPLACE FUNCTION update_system_setting(
  key_name TEXT,
  new_value JSONB,
  user_id UUID DEFAULT auth.uid()
)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user is admin (if profiles table exists and has role column)
  IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='profiles' AND column_name='role') THEN
    IF NOT EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = user_id 
      AND profiles.role = 'admin'
    ) THEN
      RAISE EXCEPTION 'Access denied: Admin role required';
    END IF;
  END IF;

  -- Update or insert setting
  INSERT INTO system_settings (setting_key, setting_value, updated_by, updated_at)
  VALUES (key_name, new_value, user_id, NOW())
  ON CONFLICT (setting_key) 
  DO UPDATE SET 
    setting_value = EXCLUDED.setting_value,
    updated_by = EXCLUDED.updated_by,
    updated_at = EXCLUDED.updated_at;

  RETURN true;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error and return false
    RAISE LOG 'Error in update_system_setting: %', SQLERRM;
    RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the default service fee config to use Dalasi amounts
UPDATE system_settings 
SET setting_value = '{
  "fee_type": "tiered",
  "fixed_fee": 25.00,
  "percentage_fee": 3.0,
  "tiers": [
    {"min_amount": 0, "max_amount": 1000, "fee": 10.00, "percentage": null},
    {"min_amount": 1001, "max_amount": null, "fee": 25.00, "percentage": null}
  ],
  "effective_date": null,
  "grace_period_days": 7
}'::jsonb
WHERE setting_key = 'service_fee_config';

-- Insert if it doesn't exist
INSERT INTO system_settings (setting_key, setting_value, description, category) VALUES
('service_fee_config', '{
  "fee_type": "tiered",
  "fixed_fee": 25.00,
  "percentage_fee": 3.0,
  "tiers": [
    {"min_amount": 0, "max_amount": 1000, "fee": 10.00, "percentage": null},
    {"min_amount": 1001, "max_amount": null, "fee": 25.00, "percentage": null}
  ],
  "effective_date": null,
  "grace_period_days": 7
}', 'Service fee configuration for transactions (amounts in Gambian Dalasi)', 'payments')
ON CONFLICT (setting_key) DO NOTHING;

-- Insert sample stores for The Gambia
INSERT INTO stores (name, slug, description, logo, cover_image, contact_email, contact_phone, address, city, state, country, featured, rating, review_count) VALUES
('TechHub Gambia', 'techhub-gambia', 'Your one-stop shop for all things tech in The Gambia', 
 'https://images.unsplash.com/photo-1535303311164-664fc9ec6532?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
 'https://images.unsplash.com/photo-1478860409698-8707f313ee8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
 '<EMAIL>', '+2207123456', 'Kairaba Avenue', 'Serrekunda', 'Greater Banjul Area', 'The Gambia', true, 4.5, 120),

('Fashion Forward GM', 'fashion-forward-gm', 'Latest trends in fashion for Gambian style', 
 'https://images.unsplash.com/photo-1507679799987-c73779587ccf?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
 'https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
 '<EMAIL>', '+2207234567', 'Westfield Junction', 'Serrekunda', 'Greater Banjul Area', 'The Gambia', true, 4.2, 85),

('Home Essentials GM', 'home-essentials-gm', 'Everything you need for your Gambian home', 
 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
 'https://images.unsplash.com/photo-1484154218962-a197022b5858?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
 '<EMAIL>', '+2207345678', 'Bakau New Town', 'Bakau', 'Greater Banjul Area', 'The Gambia', true, 4.7, 150),

('Sports World GM', 'sports-world-gm', 'Gear for every sport in The Gambia', 
 'https://images.unsplash.com/photo-1517649763962-0c623066013b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
 '<EMAIL>', '+2207456789', 'Banjul Road', 'Banjul', 'Greater Banjul Area', 'The Gambia', true, 4.3, 95)
ON CONFLICT (slug) DO NOTHING;

-- Insert sample categories with Gambian context
INSERT INTO categories (name, slug, description, image, featured) VALUES
('Electronics', 'electronics', 'Electronic devices and gadgets for modern Gambian homes', 
 'https://images.unsplash.com/photo-1498049794561-7780e7231661?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', true),
('Fashion & Clothing', 'fashion-clothing', 'Fashion and apparel suitable for Gambian climate and style', 
 'https://images.unsplash.com/photo-1523381210434-271e8be1f52b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', true),
('Home & Kitchen', 'home-kitchen', 'Products for your Gambian home and kitchen', 
 'https://images.unsplash.com/photo-1556911220-bff31c812dba?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', true),
('Books & Media', 'books-media', 'Books and literature including African authors', 
 'https://images.unsplash.com/photo-1495446815901-a7297e633e8d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', false),
('Sports & Outdoors', 'sports-outdoors', 'Sports equipment and outdoor gear for Gambian activities', 
 'https://images.unsplash.com/photo-1517649763962-0c623066013b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80', true)
ON CONFLICT (slug) DO NOTHING;

-- Insert sample products with Dalasi pricing (1 USD ≈ 67 GMD)
INSERT INTO products (name, slug, description, price, compare_at_price, currency, category_id, store_id, featured, trending, rating, review_count) VALUES
-- Electronics from TechHub Gambia
('Wireless Earbuds Pro', 'wireless-earbuds-pro', 'High-quality wireless earbuds with noise cancellation, perfect for Gambian lifestyle', 
 5360.00, 6700.00, 'GMD', 
 (SELECT id FROM categories WHERE slug = 'electronics' LIMIT 1),
 (SELECT id FROM stores WHERE slug = 'techhub-gambia' LIMIT 1),
 true, true, 4.5, 89),

('Smart Watch Fitness', 'smart-watch-fitness', 'Fitness tracker and smartwatch with heart rate monitor, ideal for Gambian climate', 
 10050.00, 13400.00, 'GMD',
 (SELECT id FROM categories WHERE slug = 'electronics' LIMIT 1),
 (SELECT id FROM stores WHERE slug = 'techhub-gambia' LIMIT 1),
 true, false, 4.2, 67),

-- Fashion from Fashion Forward GM
('African Print Shirt', 'african-print-shirt', 'Beautiful African print shirt perfect for Gambian occasions', 
 1340.00, 1675.00, 'GMD',
 (SELECT id FROM categories WHERE slug = 'fashion-clothing' LIMIT 1),
 (SELECT id FROM stores WHERE slug = 'fashion-forward-gm' LIMIT 1),
 true, true, 4.6, 124),

('Traditional Gambian Dress', 'traditional-gambian-dress', 'Elegant traditional Gambian dress for special occasions', 
 3350.00, 4690.00, 'GMD',
 (SELECT id FROM categories WHERE slug = 'fashion-clothing' LIMIT 1),
 (SELECT id FROM stores WHERE slug = 'fashion-forward-gm' LIMIT 1),
 true, false, 4.8, 156),

-- Home items from Home Essentials GM
('Solar Powered Fan', 'solar-powered-fan', 'Energy-efficient solar fan perfect for Gambian homes', 
 4690.00, 6030.00, 'GMD',
 (SELECT id FROM categories WHERE slug = 'home-kitchen' LIMIT 1),
 (SELECT id FROM stores WHERE slug = 'home-essentials-gm' LIMIT 1),
 true, true, 4.4, 78),

('Local Cookware Set', 'local-cookware-set', 'Traditional and modern cookware set for Gambian cuisine', 
 8710.00, 10720.00, 'GMD',
 (SELECT id FROM categories WHERE slug = 'home-kitchen' LIMIT 1),
 (SELECT id FROM stores WHERE slug = 'home-essentials-gm' LIMIT 1),
 false, false, 4.7, 92)
ON CONFLICT (slug) DO NOTHING;

-- Function to update store ratings when reviews are added/updated
CREATE OR REPLACE FUNCTION update_store_rating_from_reviews()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the store's average rating and review count from reviews table
  UPDATE stores 
  SET 
    rating = (
      SELECT ROUND(AVG(rating)::numeric, 2)
      FROM reviews 
      WHERE store_id = COALESCE(NEW.store_id, OLD.store_id)
    ),
    review_count = (
      SELECT COUNT(*)
      FROM reviews 
      WHERE store_id = COALESCE(NEW.store_id, OLD.store_id)
    ),
    updated_at = NOW()
  WHERE id = COALESCE(NEW.store_id, OLD.store_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger for store rating updates from reviews
DROP TRIGGER IF EXISTS update_store_rating_from_reviews_trigger ON reviews;
CREATE TRIGGER update_store_rating_from_reviews_trigger
  AFTER INSERT OR UPDATE OR DELETE ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_store_rating_from_reviews();

-- Enable RLS on all tables if not already enabled
ALTER TABLE stores ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;

-- Fix the reviews table to allow store-only reviews (no product required)
ALTER TABLE reviews ALTER COLUMN product_id DROP NOT NULL;
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_user_id_product_id_key;
-- Add new unique constraint for store reviews
ALTER TABLE reviews ADD CONSTRAINT reviews_user_store_unique 
  UNIQUE(user_id, store_id) DEFERRABLE INITIALLY DEFERRED;

-- Create policies for public read access
CREATE POLICY IF NOT EXISTS "Anyone can view stores" ON stores FOR SELECT USING (true);
CREATE POLICY IF NOT EXISTS "Anyone can view products" ON products FOR SELECT USING (true);
CREATE POLICY IF NOT EXISTS "Anyone can view categories" ON categories FOR SELECT USING (true);
CREATE POLICY IF NOT EXISTS "Anyone can view reviews" ON reviews FOR SELECT USING (true);

-- Allow authenticated users to create reviews
CREATE POLICY IF NOT EXISTS "Authenticated users can create reviews" ON reviews 
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY IF NOT EXISTS "Users can update their own reviews" ON reviews 
  FOR UPDATE USING (auth.uid() = user_id);

-- Insert sample delivered orders for testing rating system
-- Note: These will only work if you have actual user accounts
-- You can modify the user_id values to match real user IDs from your auth.users table

-- Sample delivered order 1
INSERT INTO orders (
  id, user_id, status, subtotal, service_fee, delivery_fee, total, currency, 
  delivery_method, shipping_name, shipping_address, shipping_city, shipping_state, 
  shipping_country, created_at, updated_at
) VALUES (
  'sample-delivered-order-1',
  '********-0000-0000-0000-********0001', -- Replace with actual user ID
  'delivered',
  500.00, 10.00, 15.00, 525.00, 'GMD',
  'delivery', 'John Doe', 'Kairaba Avenue', 'Serrekunda', 'Greater Banjul Area',
  'The Gambia', NOW() - INTERVAL '7 days', NOW() - INTERVAL '1 day'
) ON CONFLICT (id) DO NOTHING;

-- Sample order items for delivered order 1
INSERT INTO order_items (
  order_id, product_id, store_id, quantity, price, total, currency
) VALUES 
(
  'sample-delivered-order-1',
  (SELECT id FROM products WHERE slug = 'wireless-earbuds-pro' LIMIT 1),
  (SELECT id FROM stores WHERE slug = 'techhub-gambia' LIMIT 1),
  1, 250.00, 250.00, 'GMD'
),
(
  'sample-delivered-order-1',
  (SELECT id FROM products WHERE slug = 'african-print-shirt' LIMIT 1),
  (SELECT id FROM stores WHERE slug = 'fashion-forward-gm' LIMIT 1),
  2, 125.00, 250.00, 'GMD'
) ON CONFLICT DO NOTHING;

-- Sample delivered order 2
INSERT INTO orders (
  id, user_id, status, subtotal, service_fee, delivery_fee, total, currency, 
  delivery_method, shipping_name, shipping_address, shipping_city, shipping_state, 
  shipping_country, created_at, updated_at
) VALUES (
  'sample-delivered-order-2',
  '********-0000-0000-0000-********0001', -- Replace with actual user ID
  'delivered',
  800.00, 25.00, 15.00, 840.00, 'GMD',
  'delivery', 'John Doe', 'Bakau New Town', 'Bakau', 'Greater Banjul Area',
  'The Gambia', NOW() - INTERVAL '14 days', NOW() - INTERVAL '3 days'
) ON CONFLICT (id) DO NOTHING;

-- Sample order items for delivered order 2
INSERT INTO order_items (
  order_id, product_id, store_id, quantity, price, total, currency
) VALUES 
(
  'sample-delivered-order-2',
  (SELECT id FROM products WHERE slug = 'solar-powered-fan' LIMIT 1),
  (SELECT id FROM stores WHERE slug = 'home-essentials-gm' LIMIT 1),
  1, 400.00, 400.00, 'GMD'
),
(
  'sample-delivered-order-2',
  (SELECT id FROM products WHERE slug = 'local-cookware-set' LIMIT 1),
  (SELECT id FROM stores WHERE slug = 'home-essentials-gm' LIMIT 1),
  1, 400.00, 400.00, 'GMD'
) ON CONFLICT DO NOTHING;