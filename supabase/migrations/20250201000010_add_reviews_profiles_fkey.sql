-- Add foreign key relationship between reviews.user_id and profiles.id
-- This allows the StoreReviews component to join reviews with profiles

-- Add the foreign key constraint
-- Note: This assumes that all user_ids in reviews table already exist in profiles table
-- If there are orphaned records, they need to be cleaned up first

DO $$
BEGIN
    -- Check if the foreign key constraint already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'reviews_user_id_profiles_fkey'
        AND table_name = 'reviews'
    ) THEN
        -- Add the foreign key constraint
        ALTER TABLE reviews 
        ADD CONSTRAINT reviews_user_id_profiles_fkey 
        FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Add an index for better join performance
CREATE INDEX IF NOT EXISTS reviews_user_id_profiles_idx ON reviews(user_id);-- Add foreign key relationship between reviews.user_id and profiles.id
-- This allows the StoreReviews component to join reviews with profiles

-- Add the foreign key constraint
-- Note: This assumes that all user_ids in reviews table already exist in profiles table
-- If there are orphaned records, they need to be cleaned up first

DO $$
BEGIN
    -- Check if the foreign key constraint already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'reviews_user_id_profiles_fkey'
        AND table_name = 'reviews'
    ) THEN
        -- Add the foreign key constraint
        ALTER TABLE reviews 
        ADD CONSTRAINT reviews_user_id_profiles_fkey 
        FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Add an index for better join performance
CREATE INDEX IF NOT EXISTS reviews_user_id_profiles_idx ON reviews(user_id);