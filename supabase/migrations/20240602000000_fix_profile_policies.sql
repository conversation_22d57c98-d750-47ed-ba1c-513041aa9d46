-- Fix the infinite recursion in profile policies
-- First, drop the problematic policies that cause infinite recursion
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Admins can update all profiles" ON profiles;

-- Create a new policy for admins to view all profiles without recursion
-- This uses a direct role check instead of a subquery
CREATE POLICY "Ad<PERSON> can view all profiles fixed"
  ON profiles FOR SELECT
  TO authenticated
  USING (
    -- Either it's the user's own profile OR the current user has admin role
    auth.uid() = id OR 
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Create a new policy for admins to update all profiles without recursion
CREATE POLICY "Admins can update all profiles fixed"
  ON profiles FOR UPDATE
  TO authenticated
  USING (
    -- Either it's the user's own profile OR the current user has admin role
    auth.uid() = id OR 
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Make sure the user's own profile policy still exists
-- If it doesn't, create it
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'profiles' 
    AND policyname = 'Users can view their own profile'
  ) THEN
    CREATE POLICY "Users can view their own profile"
      ON profiles FOR SELECT
      USING (auth.uid() = id);
  END IF;
  
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename = 'profiles' 
    AND policyname = 'Users can update their own profile'
  ) THEN
    CREATE POLICY "Users can update their own profile"
      ON profiles FOR UPDATE
      USING (auth.uid() = id);
  END IF;
END
$$;
