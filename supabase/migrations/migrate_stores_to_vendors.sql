-- Migration: Convert Stores to Vendors

-- Create vendors table
CREATE TABLE IF NOT EXISTS vendors (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  contact_email TEXT,
  contact_phone TEXT,
  commission_rate DECIMAL(5,2) DEFAULT 10.00,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'suspended')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS vendors_status_idx ON vendors(status);
CREATE INDEX IF NOT EXISTS vendors_name_idx ON vendors(name);

-- Migrate existing stores data to vendors
INSERT INTO vendors (id, name, contact_email, contact_phone, status, created_at, updated_at)
SELECT
  id,
  name,
  contact_email,
  contact_phone,
  CASE
    WHEN status = 'active' THEN 'active'
    ELSE 'suspended'
  END as status,
  created_at,
  updated_at
FROM stores
ON CONFLICT (id) DO NOTHING;

-- Add vendor_id columns and migrate data
ALTER TABLE products ADD COLUMN IF NOT EXISTS vendor_id UUID REFERENCES vendors(id);
UPDATE products SET vendor_id = store_id WHERE store_id IS NOT NULL AND vendor_id IS NULL;

ALTER TABLE order_items ADD COLUMN IF NOT EXISTS vendor_id UUID REFERENCES vendors(id);
UPDATE order_items SET vendor_id = store_id WHERE store_id IS NOT NULL AND vendor_id IS NULL;

ALTER TABLE payouts ADD COLUMN IF NOT EXISTS vendor_id UUID REFERENCES vendors(id);
UPDATE payouts SET vendor_id = store_id WHERE store_id IS NOT NULL AND vendor_id IS NULL;

ALTER TABLE reviews ADD COLUMN IF NOT EXISTS vendor_id UUID REFERENCES vendors(id);
UPDATE reviews SET vendor_id = store_id WHERE store_id IS NOT NULL AND vendor_id IS NULL;

-- Create order_vendor_items table
CREATE TABLE IF NOT EXISTS order_vendor_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  vendor_id UUID NOT NULL REFERENCES vendors(id),
  total_amount DECIMAL(10, 2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'GMD',
  status TEXT NOT NULL DEFAULT 'pending',
  rejection_reason TEXT,
  rejection_notes TEXT,
  accepted_at TIMESTAMP WITH TIME ZONE,
  rejected_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(order_id, vendor_id)
);

INSERT INTO order_vendor_items (
  order_id, vendor_id, total_amount, currency, status,
  rejection_reason, rejection_notes, accepted_at, rejected_at,
  created_at, updated_at
)
SELECT
  order_id, store_id as vendor_id, total_amount, currency, status,
  rejection_reason, rejection_notes, accepted_at, rejected_at,
  created_at, updated_at
FROM order_store_items
ON CONFLICT (order_id, vendor_id) DO NOTHING;

CREATE INDEX IF NOT EXISTS order_vendor_items_order_id_idx ON order_vendor_items(order_id);
CREATE INDEX IF NOT EXISTS order_vendor_items_vendor_id_idx ON order_vendor_items(vendor_id);
CREATE INDEX IF NOT EXISTS order_vendor_items_status_idx ON order_vendor_items(status);

-- Create updated_at trigger for vendors
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_vendors_updated_at
    BEFORE UPDATE ON vendors
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
