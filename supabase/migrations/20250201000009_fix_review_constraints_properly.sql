-- Fix review constraints to allow users to review each product individually
-- Users should be able to review multiple products from the same store
-- But only review each specific product once

-- Drop the incorrect constraint that prevents multiple product reviews per store
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_user_product_or_store_unique;

-- Restore the correct constraint: users can review each product once
-- This allows multiple product reviews per store, but prevents duplicate reviews of the same product
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_user_id_product_id_key;
ALTER TABLE reviews ADD CONSTRAINT reviews_user_id_product_id_key 
  UNIQUE(user_id, product_id);

-- Make product_id nullable to allow direct store reviews (reviews without a specific product)
ALTER TABLE reviews ALTER COLUMN product_id DROP NOT NULL;

-- Add a check constraint to ensure we have either a product review or a store-only review
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_product_or_store_check;
ALTER TABLE reviews ADD CONSTRAINT reviews_product_or_store_check 
  CHECK (product_id IS NOT NULL OR store_id IS NOT NULL);

-- For store-only reviews (where product_id is NULL), ensure one review per user per store
-- This is a separate constraint for direct store reviews
CREATE UNIQUE INDEX IF NOT EXISTS reviews_user_store_only_unique 
  ON reviews(user_id, store_id) 
  WHERE product_id IS NULL;

-- Update the trigger function to calculate store ratings from all reviews
CREATE OR REPLACE FUNCTION update_store_rating_from_reviews()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the store's average rating and review count from all reviews for this store
  UPDATE stores 
  SET 
    rating = (
      SELECT ROUND(AVG(rating)::numeric, 2)
      FROM reviews 
      WHERE store_id = COALESCE(NEW.store_id, OLD.store_id)
    ),
    review_count = (
      SELECT COUNT(*)
      FROM reviews 
      WHERE store_id = COALESCE(NEW.store_id, OLD.store_id)
    ),
    updated_at = NOW()
  WHERE id = COALESCE(NEW.store_id, OLD.store_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Ensure the trigger exists
DROP TRIGGER IF EXISTS update_store_rating_from_reviews_trigger ON reviews;
CREATE TRIGGER update_store_rating_from_reviews_trigger
  AFTER INSERT OR UPDATE OR DELETE ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_store_rating_from_reviews();

-- Update existing store ratings based on current reviews
UPDATE stores 
SET 
  rating = COALESCE((
    SELECT ROUND(AVG(rating)::numeric, 2)
    FROM reviews 
    WHERE reviews.store_id = stores.id
  ), 0),
  review_count = COALESCE((
    SELECT COUNT(*)
    FROM reviews 
    WHERE reviews.store_id = stores.id
  ), 0),
  updated_at = NOW();-- Fix review constraints to allow users to review each product individually
-- Users should be able to review multiple products from the same store
-- But only review each specific product once

-- Drop the incorrect constraint that prevents multiple product reviews per store
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_user_product_or_store_unique;

-- Restore the correct constraint: users can review each product once
-- This allows multiple product reviews per store, but prevents duplicate reviews of the same product
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_user_id_product_id_key;
ALTER TABLE reviews ADD CONSTRAINT reviews_user_id_product_id_key 
  UNIQUE(user_id, product_id);

-- Make product_id nullable to allow direct store reviews (reviews without a specific product)
ALTER TABLE reviews ALTER COLUMN product_id DROP NOT NULL;

-- Add a check constraint to ensure we have either a product review or a store-only review
ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_product_or_store_check;
ALTER TABLE reviews ADD CONSTRAINT reviews_product_or_store_check 
  CHECK (product_id IS NOT NULL OR store_id IS NOT NULL);

-- For store-only reviews (where product_id is NULL), ensure one review per user per store
-- This is a separate constraint for direct store reviews
CREATE UNIQUE INDEX IF NOT EXISTS reviews_user_store_only_unique 
  ON reviews(user_id, store_id) 
  WHERE product_id IS NULL;

-- Update the trigger function to calculate store ratings from all reviews
CREATE OR REPLACE FUNCTION update_store_rating_from_reviews()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the store's average rating and review count from all reviews for this store
  UPDATE stores 
  SET 
    rating = (
      SELECT ROUND(AVG(rating)::numeric, 2)
      FROM reviews 
      WHERE store_id = COALESCE(NEW.store_id, OLD.store_id)
    ),
    review_count = (
      SELECT COUNT(*)
      FROM reviews 
      WHERE store_id = COALESCE(NEW.store_id, OLD.store_id)
    ),
    updated_at = NOW()
  WHERE id = COALESCE(NEW.store_id, OLD.store_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Ensure the trigger exists
DROP TRIGGER IF EXISTS update_store_rating_from_reviews_trigger ON reviews;
CREATE TRIGGER update_store_rating_from_reviews_trigger
  AFTER INSERT OR UPDATE OR DELETE ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_store_rating_from_reviews();

-- Update existing store ratings based on current reviews
UPDATE stores 
SET 
  rating = COALESCE((
    SELECT ROUND(AVG(rating)::numeric, 2)
    FROM reviews 
    WHERE reviews.store_id = stores.id
  ), 0),
  review_count = COALESCE((
    SELECT COUNT(*)
    FROM reviews 
    WHERE reviews.store_id = stores.id
  ), 0),
  updated_at = NOW();