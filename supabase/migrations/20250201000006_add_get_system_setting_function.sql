-- Add missing get_system_setting function

-- Create function to get system setting
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION get_system_setting(key_name TEXT)
RETURNS JSONB AS $$
DECLARE
  result JSONB;
BEGIN
  -- Get the setting value
  SELECT setting_value INTO result
  FROM system_settings 
  WHERE setting_key = key_name 
    AND is_active = true;
  
  -- Return the value or null if not found
  RETURN result;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error and return null
    RAISE LOG 'Error in get_system_setting: %', SQLERRM;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_system_setting(TEXT) TO authenticated;