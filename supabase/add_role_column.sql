-- Check if the role column exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_name = 'profiles' AND column_name = 'role'
  ) THEN
    -- Add role column if it doesn't exist
    ALTER TABLE profiles ADD COLUMN role TEXT NOT NULL DEFAULT 'user';
    COMMENT ON COLUMN profiles.role IS 'User role: user, store_owner, admin';
    
    -- Create index on profiles role
    CREATE INDEX IF NOT EXISTS profiles_role_idx ON profiles(role);
    
    RAISE NOTICE 'Added role column to profiles table';
  ELSE
    RAISE NOTICE 'Role column already exists in profiles table';
  END IF;
END
$$;

-- Set your user as admin
-- Replace '<EMAIL>' with your actual email
UPDATE profiles
SET role = 'admin'
WHERE email = '<EMAIL>';

-- Verify the change
SELECT id, email, role FROM profiles WHERE email = '<EMAIL>';
