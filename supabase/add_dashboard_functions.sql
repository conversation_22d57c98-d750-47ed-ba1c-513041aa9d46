-- Create functions for dashboard stats
-- These functions are used to get statistics for the admin dashboard

-- Function to get profiles count
CREATE OR REPLACE FUNCTION get_profiles_count()
RETURNS integer
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT COUNT(*)::integer FROM profiles;
$$;

-- Function to get stores count
CREATE OR REPLACE FUNCTION get_stores_count()
RETURNS integer
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT COUNT(*)::integer FROM stores;
$$;

-- Function to get products count
CREATE OR REPLACE FUNCTION get_products_count()
RETURNS integer
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT COUNT(*)::integer FROM products;
$$;

-- Function to get orders count
CREATE OR REPLACE FUNCTION get_orders_count()
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'orders'
  ) THEN
    RETURN (SELECT COUNT(*)::integer FROM orders);
  ELSE
    RETURN 0;
  END IF;
END;
$$;

-- Function to get total sales
CREATE OR REPLACE FUNCTION get_total_sales()
RETURNS numeric
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'orders'
  ) THEN
    RETURN COALESCE((SELECT SUM(total) FROM orders), 0);
  ELSE
    RETURN 0;
  END IF;
END;
$$;

-- Function to get recent orders
CREATE OR REPLACE FUNCTION get_recent_orders()
RETURNS TABLE (
  id uuid,
  user_id uuid,
  user_email text,
  status text,
  total numeric,
  currency text,
  created_at timestamp with time zone,
  updated_at timestamp with time zone
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  IF EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'orders'
  ) THEN
    RETURN QUERY
    SELECT 
      o.id,
      o.user_id,
      p.email as user_email,
      o.status,
      o.total,
      o.currency,
      o.created_at,
      o.updated_at
    FROM orders o
    LEFT JOIN profiles p ON o.user_id = p.id
    ORDER BY o.created_at DESC
    LIMIT 5;
  ELSE
    RETURN;
  END IF;
END;
$$;

-- Function to get recent users
CREATE OR REPLACE FUNCTION get_recent_users()
RETURNS TABLE (
  id uuid,
  email text,
  first_name text,
  last_name text,
  role text,
  created_at timestamp with time zone,
  updated_at timestamp with time zone
)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT 
    id,
    email,
    first_name,
    last_name,
    role,
    created_at,
    updated_at
  FROM profiles
  ORDER BY created_at DESC
  LIMIT 5;
$$;
