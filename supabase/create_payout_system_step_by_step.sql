-- Step-by-step creation of enhanced payout system
-- Run this script to create the payout system from scratch

-- STEP 1: Create payout_status enum if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'payout_status') THEN
        CREATE TYPE payout_status AS ENUM ('pending', 'processing', 'sent', 'completed', 'failed');
        RAISE NOTICE 'Created payout_status enum';
    ELSE
        RAISE NOTICE 'payout_status enum already exists';
    END IF;
END $$;

-- STEP 2: Create payouts table if it doesn't exist
CREATE TABLE IF NOT EXISTS payouts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    store_id UUID NOT NULL,
    order_id UUID NOT NULL,
    amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    commission DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) NOT NULL DEFAULT 'GMD',
    payout_status payout_status NOT NULL DEFAULT 'pending',
    transaction_id VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- STEP 3: Add missing columns to existing payouts table
DO $$ 
BEGIN
    -- Add commission column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'payouts' AND column_name = 'commission') THEN
        ALTER TABLE payouts ADD COLUMN commission DECIMAL(10,2) DEFAULT 0.00;
        RAISE NOTICE 'Added commission column to payouts table';
    END IF;

    -- Add transaction_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'payouts' AND column_name = 'transaction_id') THEN
        ALTER TABLE payouts ADD COLUMN transaction_id VARCHAR(255);
        RAISE NOTICE 'Added transaction_id column to payouts table';
    END IF;

    -- Add notes column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'payouts' AND column_name = 'notes') THEN
        ALTER TABLE payouts ADD COLUMN notes TEXT;
        RAISE NOTICE 'Added notes column to payouts table';
    END IF;
END $$;

-- STEP 4: Create indexes for payouts table
CREATE INDEX IF NOT EXISTS idx_payouts_store_id ON payouts(store_id);
CREATE INDEX IF NOT EXISTS idx_payouts_order_id ON payouts(order_id);
CREATE INDEX IF NOT EXISTS idx_payouts_status ON payouts(payout_status);
CREATE INDEX IF NOT EXISTS idx_payouts_created_at ON payouts(created_at);

-- STEP 5: Create receipts table
CREATE TABLE IF NOT EXISTS receipts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    receipt_id VARCHAR(50) UNIQUE NOT NULL,
    payout_id UUID NOT NULL REFERENCES payouts(id) ON DELETE CASCADE,
    store_id UUID NOT NULL,
    order_id UUID NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    commission DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'GMD',
    transaction_id VARCHAR(255),
    receipt_data JSONB,
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- STEP 6: Create indexes for receipts table
CREATE INDEX IF NOT EXISTS idx_receipts_payout_id ON receipts(payout_id);
CREATE INDEX IF NOT EXISTS idx_receipts_store_id ON receipts(store_id);
CREATE INDEX IF NOT EXISTS idx_receipts_order_id ON receipts(order_id);
CREATE INDEX IF NOT EXISTS idx_receipts_generated_at ON receipts(generated_at);

-- STEP 7: Create finder_earnings table
CREATE TABLE IF NOT EXISTS finder_earnings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID NOT NULL,
    payout_id UUID REFERENCES payouts(id) ON DELETE SET NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'GMD',
    earning_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- STEP 8: Create indexes for finder_earnings table
CREATE INDEX IF NOT EXISTS idx_finder_earnings_order_id ON finder_earnings(order_id);
CREATE INDEX IF NOT EXISTS idx_finder_earnings_payout_id ON finder_earnings(payout_id);
CREATE INDEX IF NOT EXISTS idx_finder_earnings_earning_date ON finder_earnings(earning_date);

-- STEP 9: Update existing payouts to have commission data (if any exist)
UPDATE payouts 
SET commission = ROUND(amount * 0.0526, 2) -- 5% commission calculation
WHERE commission IS NULL OR commission = 0;

-- STEP 10: Create function for updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- STEP 11: Create triggers for updated_at columns
DROP TRIGGER IF EXISTS trigger_payouts_updated_at ON payouts;
CREATE TRIGGER trigger_payouts_updated_at
    BEFORE UPDATE ON payouts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_receipts_updated_at ON receipts;
CREATE TRIGGER trigger_receipts_updated_at
    BEFORE UPDATE ON receipts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_finder_earnings_updated_at ON finder_earnings;
CREATE TRIGGER trigger_finder_earnings_updated_at
    BEFORE UPDATE ON finder_earnings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- STEP 12: Verification
SELECT 'SETUP COMPLETE - VERIFICATION:' as section;

-- Check payouts table
SELECT 'Payouts table columns:' as info;
SELECT column_name, data_type, is_nullable
FROM information_schema.columns
WHERE table_name = 'payouts'
ORDER BY ordinal_position;

-- Check receipts table
SELECT 'Receipts table exists:' as info;
SELECT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'receipts'
) as receipts_exists;

-- Check finder_earnings table
SELECT 'Finder earnings table exists:' as info;
SELECT EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_name = 'finder_earnings'
) as finder_earnings_exists;

-- Check enum values
SELECT 'Payout status enum values:' as info;
SELECT enumlabel as status_value
FROM pg_enum
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'payout_status')
ORDER BY enumsortorder;

SELECT 'SETUP COMPLETED SUCCESSFULLY!' as result;
